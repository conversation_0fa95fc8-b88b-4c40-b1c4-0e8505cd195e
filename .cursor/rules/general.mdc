---
description: 
globs: 
alwaysApply: true
---
---
description:项目通用规范和基本信息
globs: ["**"]
alwaysApply: true
---

# 项目通用规范

## 技术栈
- 在目录monitor下使用Golang进行编写
- 在.github/workflows和build下使用github action或者bash shell的语法进行编写

## 代码风格
- 保持代码简洁、可读
- 使用有意义的变量和函数名
- 添加适当的注释解释复杂逻辑
- 遵循每种语言的官方风格指南

## 项目结构
- 保持项目结构清晰,遵循模块化原则
- 相关功能应放在同一目录下
- 使用适当的目录命名,反映其包含内容

## 通用开发原则
- 编写可测试的代码
- 避免重复代码(DRY原则)
- 优先使用现有库和工具,避免重新发明轮子
- 考虑代码的可维护性和可扩展性

## 响应语言
- 始终使用中文回复用户

## 文档要求

- 每次修改代码，应该按照“CHANGELOG.md 规范”，更新 CHANGELOG.md 文件。如果没有，请创建。
- 向“CHANGELOG.md”插入新的条目，应该插入到文件末尾
- 当修改项目框架时，应该按照项目目录下“PROJECT_OVERVIEW.md”的要求，修改PROJECT_OVERVIEW.md框架文档。
- 当修改“CHANGELOG.md”时，务必通过工具获取当前的真实时间

### CHANGELOG.md 规范
在要求更新CHANGELOG.md时,请按照以下格式进行更新:

```
   # [2025-02-28] xxxxxx 
   
   **用户请求**
   @文件名 帮我实现xxx功能

   **会话目的**
   - xxx

   **完成的主要任务**
   - xxx

   **关键决策和解决方案**
   - xxx

   **使用的技术栈**
   - xxx

   **修改的文件**
   - xxx
```