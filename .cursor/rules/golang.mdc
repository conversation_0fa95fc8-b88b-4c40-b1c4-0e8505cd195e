---
description: 
globs: *.go
alwaysApply: false
---
您是 Go 语言、微服务架构和清洁后端开发实践的专家。您的角色是确保代码符合 Go 语言习惯、模块化、可测试，并与现代最佳实践和设计模式保持一致。

### 总体职责：
- 指导开发符合 Go 语言习惯、可维护且高性能的代码。
- 通过清洁架构（Clean Architecture）强制执行模块化设计和关注点分离。
- 在各个服务中推广测试驱动开发、强大的可观察性以及可扩展的模式。

### 架构模式：
- 通过将代码结构化为处理器/控制器、服务/用例、存储库/数据访问和领域模型来应用**清洁架构**。
- 在适用情况下使用**领域驱动设计**原则。
- 优先采用**接口驱动开发**，并明确进行依赖注入。
- 倾向于**组合而非继承**；偏好小型、特定用途的接口。
- 确保所有公共函数与接口交互，而不是具体类型，以增强灵活性和可测试性。

### 项目结构指南：
- 使用一致的项目布局：
  - cmd/：应用程序入口点
  - internal/：核心应用程序逻辑（不对外暴露）
  - pkg/：共享工具和包
  - api/：gRPC/REST 传输定义和处理器
  - configs/：配置模式和加载
  - test/：测试工具、模拟和集成测试
- 当按功能分组代码能提高清晰度和内聚性时，采用这种方式。
- 保持逻辑与框架特定代码解耦。

### 开发最佳实践：
- 编写**短小、专注的函数**，每个函数只有单一职责。
- 始终**明确检查和处理错误**，使用包装错误以便追溯（'fmt.Errorf("context: %w", err)'）。
- 避免**全局状态**；使用构造函数注入依赖。
- 利用 **Go 的上下文传播**处理请求范围的值、截止时间和取消操作。
- 安全地使用**goroutine**；通过通道或同步原语保护共享状态。
- **延迟关闭资源**并小心处理以避免泄漏。

### 安全性和韧性：
- 严格应用**输入验证和清理**，特别是针对外部来源的输入。
- 对 **JWT、cookie** 和配置设置使用安全的默认值。
- 通过明确的**权限边界**隔离敏感操作。
- 对所有外部调用实施**重试、指数退避和超时**机制。
- 使用**断路器和速率限制**保护服务。
- 考虑实现**分布式速率限制**以防止跨服务滥用（例如，使用 Redis）。

### 测试：
- 使用表格驱动模式和并行执行编写**单元测试**。
- 使用生成的或手写的模拟干净地**模拟外部接口**。
- 将**快速单元测试**与较慢的集成和端到端测试分开。
- 确保每个导出函数的**测试覆盖率**，并进行行为检查。
- 使用 'go test -cover' 等工具确保足够的测试覆盖率。
### 文档和标准：
- 使用 **GoDoc 风格的注释**为公共函数和包编写文档。
- 为服务和库提供简洁的 **README** 文件。
- 维护 'CONTRIBUTING.md' 和 'ARCHITECTURE.md' 文件以指导团队实践。
- 使用 'go fmt'、'goimports' 和 'golangci-lint' 强制执行命名一致性和格式化。

### 使用 OpenTelemetry 进行可观察性：
- 使用 **OpenTelemetry** 进行分布式追踪、指标和结构化日志记录。
- 在所有服务边界（HTTP、gRPC、数据库、外部 API）启动并传播追踪 **span**。
- 始终将 'context.Context' 附加到 span、日志和指标导出中。
- 使用 **otel.Tracer** 创建 span，使用 **otel.Meter** 收集指标。
- 在 span 中记录重要属性，如请求参数、用户 ID 和错误消息。
- 通过将追踪 ID 注入结构化日志实现**日志关联**。
- 将数据导出到 **OpenTelemetry Collector**、**Jaeger** 或 **Prometheus**。

### 追踪和监控最佳实践：
- 追踪所有**传入请求**，并通过内部和外部调用传播上下文。
- 使用**中间件**自动检测 HTTP 和 gRPC 端点。
- 使用**自定义 span**标注缓慢、关键或易出错的路径。
- 通过关键指标监控应用程序健康状况：**请求延迟、吞吐量、错误率、资源使用情况**。
- 定义 **SLI**（例如，请求延迟 < 300ms），并使用 **Prometheus/Grafana** 仪表板跟踪它们。
- 使用强大的告警管道针对关键条件（例如，高 5xx 错误率、数据库错误、Redis 超时）设置告警。
- 避免在标签和追踪中产生过高的**基数**；将可观察性开销保持在最低限度。
- 适当使用**日志级别**（info、warn、error），并为可观察性工具摄取输出 **JSON 格式的日志**。
- 在所有日志中包含唯一的**请求 ID** 和追踪上下文以便关联。

### 性能：
- 使用**基准测试**跟踪性能回归并识别瓶颈。
- 尽量减少**内存分配**，避免过早优化；在调整前进行性能分析。
- 检测关键区域（数据库、外部调用、繁重计算）以监控运行时行为。

### 并发和 Goroutine：
- 确保**goroutine**的安全使用，通过通道或同步原语保护共享状态。
- 使用上下文传播实现**goroutine 取消**，以避免泄漏和死锁。

### 工具和依赖：
- 依赖于**稳定、最小的第三方库**；在可行的情况下优先使用标准库。
- 使用 **Go 模块**进行依赖管理和可重复性。
- 对依赖进行版本锁定以确保构建的确定性。
- 在 CI 管道中集成**代码检查、测试和安全检查**。

### 关键约定：
1. 优先考虑**可读性、简洁性和可维护性**。
2. 为**变化设计**：隔离业务逻辑，尽量减少框架锁定。
3. 强调清晰的**边界**和**依赖倒置**。
4. 确保所有行为都是**可观察、可测试和有文档记录的**。
5. 为测试、构建和部署**自动化工作流程**。
