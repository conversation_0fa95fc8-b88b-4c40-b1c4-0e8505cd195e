name: Nightly Taptest

on:
  workflow_dispatch:
    inputs:
      JAVA_VERSION:
        description: 'Java version to run tests'
        required: true
        type: choice
        default: '17.0.14-zulu'
        options:
          - '8.0.392-zulu'
          - '11.0.22-zulu'
          - '17.0.14-zulu'
      COMMON_LIB_BRANCH:
        description: 'common lib branch'
        required: true
        type: string
        default: 'main'
      ENTERPRISE_WEB_BRANCH:
        description: 'enterprise web branch'
        required: true
        type: string
        default: 'develop'
      TAPDATA_BRANCH:
        description: 'tapdata branch'
        required: true
        type: string
        default: 'develop'
      TAPDATA_ENTERPRISE_BRANCH:
        description: 'tapdata enterprise branch'
        required: true
        type: string
        default: 'develop'
      CONNECTORS_BRANCH:
        description: 'connectors branch'
        required: true
        type: string
        default: 'develop'
      CONNECTORS_ENTERPRISE_BRANCH:
        description: 'connectors enterprise branch'
        required: true
        type: string
        default: 'develop'
      CASES:
        description: 'Test case IDs (leave blank to run all nightly cases)'
        required: false
        type: string

  schedule:
    - cron: '0 16 * * *'

jobs:
  Run-Taptest:
    runs-on: macOS
    timeout-minutes: 1440
    defaults:
      run:
        shell: /bin/bash -l {0}  # 👈 Apply this shell to all `run` steps
    steps:
      - name: Set environment variable (if triggered by workflow_dispatch)
        if: github.event_name == 'workflow_dispatch'
        run: |
          cd /Users/<USER>/t-layer3-test
          echo "customize_tapdata_branch=true" >> $GITHUB_ENV
          if [[ -f "tapdata_customize_branch.sh" ]]; then
            echo "tapdata_customize_branch.sh exists, remove it and re-create with the customized branches"
            rm tapdata_customize_branch.sh
          fi
          echo "export common_lib_branch=${{ inputs.COMMON_LIB_BRANCH }}" >> tapdata_customize_branch.sh
          echo "export enterprise_web_branch=${{ inputs.ENTERPRISE_WEB_BRANCH }}" >> tapdata_customize_branch.sh
          echo "export tapdata_enterprise_branch=${{ inputs.TAPDATA_ENTERPRISE_BRANCH }}" >> tapdata_customize_branch.sh
          echo "export tapdata_branch=${{ inputs.TAPDATA_BRANCH }}" >> tapdata_customize_branch.sh
          echo "export connectors_branch=${{ inputs.CONNECTORS_BRANCH }}" >> tapdata_customize_branch.sh
          echo "export connectors_enterprise_branch=${{ inputs.CONNECTORS_ENTERPRISE_BRANCH }}" >> tapdata_customize_branch.sh
          cat tapdata_customize_branch.sh
      - name: Set JDK version
        run: |
          # 使用固定的SDKMAN安装路径
          export SDKMAN_DIR="/Users/<USER>/.sdkman"
          source "/Users/<USER>/.sdkman/bin/sdkman-init.sh"
          
          echo "设置默认Java版本为: ${{ inputs.JAVA_VERSION || '17.0.14-zulu' }}"
          sdk default java ${{ inputs.JAVA_VERSION || '17.0.14-zulu' }}
          echo "验证Java版本是否切换成功："
          java -version
      - name: check TapTest running or not
        run: |
          RUNNING_PROCESS=$(ps aux | grep -E '\./run|\./taptest' | grep -v grep || true)
          if [[ -n "$RUNNING_PROCESS" ]]; then
            echo "TapTest is already running, abandon this run."
            exit 1
          fi
      - name: Build Tapdata
        run: |
          cd /Users/<USER>/t-layer3-test && bash ./run build
      - name: Reset the db that needs to be used
        run: |
          cd /Users/<USER>/t-layer3-test && ./run reset_db
      - name: Start Tapdata
        run: |
          cd /Users/<USER>/t-layer3-test && bash ./run restart_tapdata
      - name: Run - TapTest
        run: |
          if [ -n "${{ github.event.inputs.cases }}" ]; then
            echo "Running github_cases: ${{ github.event.inputs.cases }}"
            cd /Users/<USER>/t-layer3-test && chat_id=oc_33fe6d5aed3ef54486076fb211771d15 bash ./run github_cases "${{ github.event.inputs.cases }}"
          else
            echo "Running github_nightly"
            cd /Users/<USER>/t-layer3-test && chat_id=oc_33fe6d5aed3ef54486076fb211771d15 bash ./run github_nightly
          fi
      - name: Set JDK to default version
        if: always()
        run: |
          export SDKMAN_DIR="/Users/<USER>/.sdkman"
          source "/Users/<USER>/.sdkman/bin/sdkman-init.sh"
          
          echo "测试完成，重新设置全局默认Java版本为17.0.14-zulu"
          sdk default java 17.0.14-zulu
          echo "验证Java版本是否切换成功："
          java -version
