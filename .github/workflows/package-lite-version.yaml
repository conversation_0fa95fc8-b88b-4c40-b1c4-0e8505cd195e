name: Package Lite Version

on:
  workflow_dispatch:
    inputs:
      PackageName:
        description: "Input Package Version(like v3.5.14-66191ce2): "
        required: true
        type: string
        default: ""
      OS:
        description: "OS: "
        required: true
        type: choice
        default: 'x86_64'
        options:
          - 'ubuntu-20.04'
          - 'ubuntu-22.04'
          - 'centos-7'
          - 'windows'
      IncludeJdkAndMongodb:
        description: "Include jdk and mongodb: "
        required: true
        type: boolean
        default: false
      ReleaseToOSS:
        description: "Release to oss: "
        required: true
        type: boolean
        default: false

env:
  TAPDATA_APPLICATION: main
  GITEE_USER: tapdata_1

jobs:

  Push-Code-To-GOGS:
    uses: tapdata/tapdata-application/.github/workflows/sync-code-to-office.yaml@main
    with:
      tapdata-application: main
    secrets: inherit

  Outputs:
    runs-on: office-build
    needs: Push-Code-To-GOGS
    timeout-minutes: 60
    steps:
      - name: Clean Work Space
        run: |
          rm -rf ./*
      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh
          echo "Host gitee.com" > ~/.ssh/config
          echo "  StrictHostKeyChecking no" >> ~/.ssh/config
          chmod 600 ~/.ssh/config
      - name: Checkout Tapdata Application
        run: |
          git clone -b ${{ env.TAPDATA_APPLICATION }} *************:${{ env.GITEE_USER }}/tapdata-application.git
      - name: Download Tapdata Artifact Package
        run: |
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync --password-file=/tmp/rsync.passwd -vzrt --progress rsync://root@*************:873/data/enterprise-artifact/gz/${{ inputs.PackageName }}.tar.gz ./
      - name: Download Tapdata Connectors Package
        run: |
          mkdir -p connectors/dist/
          connectors_str=$(cat tapdata-application/lite-connectors)
          IFS='#' read -ra connectors <<< "$connectors_str"
          for i in "${connectors[@]}"; do
            rsync --password-file=/tmp/rsync.passwd -vzrt --progress rsync://root@*************:873/data/enterprise-artifact/connectors/${{ inputs.PackageName }}/$i connectors/dist/
          done
      - name: Output Result Package
        run: |
          tar -xf ${{ inputs.PackageName }}.tar.gz
          if [[ -d tapdata/ ]]; then
            dir_name="tapdata"
          else
            dir_name="app"
          fi
          mv connectors/dist/ $dir_name/connectors/dist/
          if [[ ${{ inputs.IncludeJdkAndMongodb }} == "true" ]]; then
            bash tapdata-application/build/package.sh --target-dir $dir_name --os ${{ inputs.OS }} --include-license --include-jdk --include-tapcli --include-mongodb --upload-to-oss
          else
            bash tapdata-application/build/package.sh --target-dir $dir_name --os ${{ inputs.OS }} --include-license --include-tapcli --upload-to-oss
          fi
