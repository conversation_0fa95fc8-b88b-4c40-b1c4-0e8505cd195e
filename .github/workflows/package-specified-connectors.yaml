name: Package Specified Connectors and MongoDB

on:
  workflow_dispatch:
    inputs:
      ConnectorsList:
        description: "Input Connectors list(separated by #, encode with base64): "
        required: true
        type: string
        default: ""
      PackageName:
        description: "Input Package Version(like v3.5.14-66191ce2): "
        required: true
        type: string
        default: ""
      IncludeMongoDB:
        description: "Include MongoDB: "
        required: true
        type: boolean
        default: false
      OS:
        description: "OS: "
        required: true
        type: choice
        default: 'x86_64'
        options:
          - 'ubuntu-20.04'
          - 'ubuntu-22.04'
          - 'windows'

env:
  TAPDATA_APPLICATION: main
  GITEE_USER: tapdata_1

jobs:

  Push-Code-To-GOGS:
    uses: tapdata/tapdata-application/.github/workflows/sync-code-to-office.yaml@main
    with:
      tapdata-application: main
    secrets: inherit

  Outputs:
    runs-on: office-build
    needs: Push-Code-To-GOGS
    timeout-minutes: 60
    steps:
      - name: Clean Work Space
        run: |
          rm -rf ./*
      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh
          echo "Host gitee.com" > ~/.ssh/config
          echo "  StrictHostKeyChecking no" >> ~/.ssh/config
          chmod 600 ~/.ssh/config
      - name: Checkout Tapdata Application
        run: |
          git clone -b ${{ env.TAPDATA_APPLICATION }} *************:${{ env.GITEE_USER }}/tapdata-application.git
      - name: Download Tapdata Artifact Package
        run: |
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync --password-file=/tmp/rsync.passwd -vzrt --progress rsync://root@*************:873/data/enterprise-artifact/gz/${{ inputs.PackageName }}.tar.gz ./
      - name: Download Tapdata Connectors Package
        run: |
          mkdir -p connectors/dist/
          connectors_str=$(echo ${{ inputs.ConnectorsList }} | base64 -d)
          IFS='#' read -ra connectors <<< "$connectors_str"
          for i in "${connectors[@]}"; do
            rsync --password-file=/tmp/rsync.passwd -vzrt --progress rsync://root@*************:873/data/enterprise-artifact/connectors/${{ inputs.PackageName }}/$i connectors/dist/
          done
      - name: Output Result Package
        run: |
          tar -xf ${{ inputs.PackageName }}.tar.gz
          if [[ -d tapdata/ ]]; then
            mv connectors/dist/ tapdata/connectors/dist/
            if [[ "${{ inputs.IncludeMongoDB }}" == "true" ]]; then
              bash tapdata-application/build/package.sh --target-dir tapdata --os ${{ inputs.OS }} --include-mongodb
            fi
          else
            mv connectors/dist/ app/connectors/dist/
            if [[ "${{ inputs.IncludeMongoDB }}" == "true" ]]; then
              bash tapdata-application/build/package.sh --target-dir app --os ${{ inputs.OS }} --include-mongodb
            fi
          fi
          rm -rf ${{ inputs.PackageName }}.tar.gz
          if [[ -d tapdata/ ]]; then
            tar -czf ${{ inputs.PackageName }}.tar.gz -C . tapdata/
          else
            tar -czf ${{ inputs.PackageName }}.tar.gz -C . app/
          fi
          rsync --password-file=/tmp/rsync.passwd -vzrt --progress ${{ inputs.PackageName }}.tar.gz rsync://root@*************:873/data/enterprise-artifact/artifacts/
          echo '**Download Tar File:**' >> $GITHUB_STEP_SUMMARY
          echo "[Download Link](http://58.251.34.123:5244/gz/${{ inputs.PackageName }}.tar.gz)" >> $GITHUB_STEP_SUMMARY
          echo "username/password： **admin/Gotapd8!**" >> $GITHUB_STEP_SUMMARY
