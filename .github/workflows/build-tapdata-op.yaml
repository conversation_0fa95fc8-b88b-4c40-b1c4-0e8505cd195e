name: OP version complete compilation

on:
  workflow_dispatch:
    inputs:
      BUILD_CONFIG:
        description: "构建配置 (JSON格式，包含分支信息等)。示例：{\"FRONTEND_BRANCH\":\"main\",\"OPENSOURCE_BRANCH\":\"main\",\"ENTERPRISE_BRANCH\":\"main\",\"CONNECTORS_BRANCH\":\"main#main\",\"LISENSE_BRANCH\":\"main\",\"TAG_NAME\":\"custom-tag\"}。其中CONNECTORS_BRANCH格式为'tapdata-connectors分支#tapdata-connectors-enterprise分支'"
        required: true
        type: string
        default: '{"FRONTEND_BRANCH":"main","OPENSOURCE_BRANCH":"main","ENTERPRISE_BRANCH":"main","CONNECTORS_BRANCH":"","LISENSE_BRANCH":"main","TAG_NAME":""}'
      IS_TAR:
        description: "Need Tar File: "
        required: false
        type: boolean
        default: false
      FRONTEND_MODE:
        description: 'Frontend Build Mode: '
        required: true
        default: 'production'
        type: choice
        options:
          - production
          - msa
          - ikas
          - chowsangsang
          - datapp
          - normal
          - oem
          - steamory
          - onein
          - datapp_normal
      TAPDATA_PLATFORM_VERSION:
        description: "Tapdata Platform Version: "
        required: true
        type: choice
        default: 'Linux'
        options:
          - 'Linux'
          - "Windows"
      CONNECTORS-OPTIONS:
        description: "Select connectors options: "
        required: true
        type: choice
        default: 'all'
        options:
          - 'nightly-build'
          - 'performance-test'
          - 'lightweight'
          - 'all'
      RUN_TEST:
        description: "Run integration tests after build: "
        required: false
        type: boolean
        default: false
      JAVA_VERSION:
        description: "Java version for compilation: "
        required: true
        type: choice
        default: 'java17'
        options:
          - 'java8'
          - 'java11'
          - 'java17'
      INCLUDE_APISERVER:
        description: "Include ApiServer in build: "
        required: false
        type: boolean
        default: true

env:
  TAPDATA_APPLICATION: main
  GITEE_USER: tapdata_1
  RUN_TEST: false

jobs:

  Set-Env:
    runs-on: ubuntu-latest
    outputs:
      CONNECTORS_BRANCH: ${{ steps.set-outputs.outputs.CONNECTORS_BRANCH }}
      CONNECTORS_ENTERPRISE_BRANCH: ${{ steps.set-outputs.outputs.CONNECTORS_ENTERPRISE_BRANCH }}
      OPENSOURCE_BRANCH: ${{ steps.set-outputs.outputs.OPENSOURCE_BRANCH }}
      LISENSE_BRANCH: ${{ steps.set-outputs.outputs.LISENSE_BRANCH }}
      ENTERPRISE_BRANCH: ${{ steps.set-outputs.outputs.ENTERPRISE_BRANCH }}
      FRONTEND_BRANCH: ${{ steps.set-outputs.outputs.FRONTEND_BRANCH }}
      FRONTEND_MODE: ${{ steps.set-outputs.outputs.FRONTEND_MODE }}
      DEPLOY: ${{ steps.set-outputs.outputs.DEPLOY }}
      IS_TAR: ${{ steps.set-outputs.outputs.IS_TAR }}
      TAG_NAME: ${{ steps.set-outputs.outputs.TAG_NAME }}
      DOCKER_IMAGE: ${{ steps.set-outputs.outputs.DOCKER_IMAGE }}
      TAR_FILE_NAME: ${{ steps.set-outputs.outputs.TAR_FILE_NAME }}
      CONNECTORS-OPTIONS: ${{ steps.set-outputs.outputs.CONNECTORS-OPTIONS }}
      RUN_TEST: ${{ steps.set-outputs.outputs.RUN_TEST }}
      JAVA_VERSION: ${{ steps.set-outputs.outputs.JAVA_VERSION }}
      TAPDATA_PLATFORM_VERSION: ${{ steps.set-outputs.outputs.TAPDATA_PLATFORM_VERSION }}
      INCLUDE_APISERVER: ${{ steps.set-outputs.outputs.INCLUDE_APISERVER }}
    timeout-minutes: 5
    steps:
      - name: Set Env
        run: |
          CONFIG='${{ inputs.BUILD_CONFIG }}'
          OPENSOURCE_BRANCH=$(echo $CONFIG | jq -r '.OPENSOURCE_BRANCH // "main"')
          LISENSE_BRANCH=$(echo $CONFIG | jq -r '.LISENSE_BRANCH // "main"')
          ENTERPRISE_BRANCH=$(echo $CONFIG | jq -r '.ENTERPRISE_BRANCH // "main"')
          FRONTEND_BRANCH=$(echo $CONFIG | jq -r '.FRONTEND_BRANCH // "main"')
          TAG_NAME=$(echo $CONFIG | jq -r '.TAG_NAME // ""')
          CONNECTORS_BRANCH_FULL=$(echo $CONFIG | jq -r '.CONNECTORS_BRANCH // ""')
          
          connector_branch=$(echo $CONNECTORS_BRANCH_FULL | cut -d '#' -f 1)
          connector_enterprise_branch=$(echo $CONNECTORS_BRANCH_FULL | cut -d '#' -f 2)
          
          echo "OPENSOURCE_BRANCH=$OPENSOURCE_BRANCH" >> $GITHUB_ENV
          echo "LISENSE_BRANCH=$LISENSE_BRANCH" >> $GITHUB_ENV
          echo "ENTERPRISE_BRANCH=$ENTERPRISE_BRANCH" >> $GITHUB_ENV
          echo "FRONTEND_BRANCH=$FRONTEND_BRANCH" >> $GITHUB_ENV
          echo "FRONTEND_MODE=${{ inputs.FRONTEND_MODE }}" >> $GITHUB_ENV
          echo "IS_TAR=${{ inputs.IS_TAR }}" >> $GITHUB_ENV
          echo "CONNECTORS-OPTIONS=${{ inputs.CONNECTORS-OPTIONS }}" >> $GITHUB_ENV
          echo "CONNECTORS_BRANCH=$connector_branch" >> $GITHUB_ENV
          echo "CONNECTORS_ENTERPRISE_BRANCH=$connector_enterprise_branch" >> $GITHUB_ENV
          echo "TAG_NAME=$TAG_NAME" >> $GITHUB_ENV
          echo "RUN_TEST=${{ inputs.RUN_TEST }}" >> $GITHUB_ENV
          echo "JAVA_VERSION=${{ inputs.JAVA_VERSION }}" >> $GITHUB_ENV
          echo "TAPDATA_PLATFORM_VERSION=${{ inputs.TAPDATA_PLATFORM_VERSION }}" >> $GITHUB_ENV
          echo "INCLUDE_APISERVER=${{ inputs.INCLUDE_APISERVER }}" >> $GITHUB_ENV
      - name: Checkout Tapdata Code
        uses: actions/checkout@v2
        with:
          repository: 'tapdata/tapdata'
          token: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
          ref: ${{ env.OPENSOURCE_BRANCH }}
          path: tapdata
          fetch-depth: 0
      - name: Set Tag
        run: |
          cd tapdata
          if [[ -n "${{ env.TAG_NAME }}" ]]; then
            tag_name="${{ env.TAG_NAME }}"
          else
            main_tag=$(git branch --show-current | cut -d '-' -f 2)
            current_timestamp=$(date +%s)
            hex_timestamp=$(printf "%X" "$current_timestamp" | tr 'A-F' 'a-f')
            tag_name="$main_tag-$hex_timestamp"
          fi
          echo "TAG_NAME=$tag_name" >> $GITHUB_ENV
      - name: TAG=${{ env.TAG_NAME }}
        run: |
          echo "tag is: ${{ env.TAG_NAME }}"
      - name: Set Output
        id: set-outputs
        run: |
          echo "::set-output name=CONNECTORS_BRANCH::${{ env.CONNECTORS_BRANCH }}"
          echo "::set-output name=CONNECTORS_ENTERPRISE_BRANCH::${{ env.CONNECTORS_ENTERPRISE_BRANCH }}"
          echo "::set-output name=OPENSOURCE_BRANCH::${{ env.OPENSOURCE_BRANCH }}"
          echo "::set-output name=LISENSE_BRANCH::${{ env.LISENSE_BRANCH }}"
          echo "::set-output name=OPENSOURCE_CONNECTORS::${{ env.OPENSOURCE_CONNECTORS }}"
          echo "::set-output name=ENTERPRISE_CONNECTORS::${{ env.ENTERPRISE_CONNECTORS }}"
          echo "::set-output name=ENTERPRISE_BRANCH::${{ env.ENTERPRISE_BRANCH }}"
          echo "::set-output name=FRONTEND_BRANCH::${{ env.FRONTEND_BRANCH }}"
          echo "::set-output name=FRONTEND_MODE::${{ env.FRONTEND_MODE }}"
          echo "::set-output name=DEPLOY::${{ env.DEPLOY }}"
          echo "::set-output name=IS_TAR::${{ env.IS_TAR }}"
          echo "::set-output name=TAG_NAME::${{ env.TAG_NAME }}"
          echo "::set-output name=DOCKER_IMAGE::harbor.internal.tapdata.io/tapdata/tapdata/tapdata-enterprise:${{ env.TAG_NAME }}"
          echo "::set-output name=TAR_FILE_NAME::${{ env.FRONTEND_MODE }}-tapdata-enterprise-${{ env.TAG_NAME }}.tar.gz"
          echo "::set-output name=CONNECTORS-OPTIONS::${{ env.CONNECTORS-OPTIONS }}"
          echo "::set-output name=RUN_TEST::${{ env.RUN_TEST }}"
          echo "::set-output name=JAVA_VERSION::${{ env.JAVA_VERSION }}"
          echo "::set-output name=TAPDATA_PLATFORM_VERSION::${{ env.TAPDATA_PLATFORM_VERSION }}"
          echo "::set-output name=INCLUDE_APISERVER::${{ env.INCLUDE_APISERVER }}"
      - name: Show Tags
        run: |
          echo "TAG_NAME=${{ env.TAG_NAME }}"
          echo "OPENSOURCE_BRANCH=${{ env.OPENSOURCE_BRANCH }}"
          echo "LISENSE_BRANCH=${{ env.LISENSE_BRANCH }}"
          echo "ENTERPRISE_BRANCH=${{ env.ENTERPRISE_BRANCH }}"
          echo "FRONTEND_BRANCH=${{ env.FRONTEND_BRANCH }}"
          echo "FRONTEND_MODE=${{ env.FRONTEND_MODE }}"
          echo "INCLUDE_APISERVER=${{ env.INCLUDE_APISERVER }}"

  Push-Code-To-GOGS:
    uses: tapdata/tapdata-application/.github/workflows/sync-code-to-office.yaml@main
    needs: Set-Env
    with:
      tapdata: ${{ needs.Set-Env.outputs.OPENSOURCE_BRANCH }}
      tapdata-license: ${{ needs.Set-Env.outputs.LISENSE_BRANCH }}
      tapdata-enterprise: ${{ needs.Set-Env.outputs.ENTERPRISE_BRANCH }}
      tapdata-connectors: ${{ needs.Set-Env.outputs.CONNECTORS_BRANCH }}
      tapdata-connectors-enterprise: ${{ needs.Set-Env.outputs.CONNECTORS_ENTERPRISE_BRANCH }}
      tapdata-enterprise-web: ${{ needs.Set-Env.outputs.FRONTEND_BRANCH }}
      docs: enterprise-data-sources-docs
    secrets: inherit

  Build-Tapdata:
    runs-on: office-build
    needs:
      - Set-Env
      - Push-Code-To-GOGS
    timeout-minutes: 60
    steps:
      - name: Clean Directory
        run: |
          tmpdir=$(mktemp -d)
          rsync --delete-before -a --force -r $tmpdir/ ./
          rm -rf $tmpdir
      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh
          echo "Host gitee.com" > ~/.ssh/config
          echo "  StrictHostKeyChecking no" >> ~/.ssh/config
          chmod 600 ~/.ssh/config
      - name: Checkout Tapdata Enterprise Code
        run: |
          git clone -b ${{ needs.Set-Env.outputs.ENTERPRISE_BRANCH }} *************:${{ env.GITEE_USER }}/tapdata-enterprise.git
          cd tapdata-enterprise && git fetch --tags
      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh
          echo "Host gitee.com" > ~/.ssh/config
          echo "  StrictHostKeyChecking no" >> ~/.ssh/config
          chmod 600 ~/.ssh/config
      - name: Checkout Tapdata Code
        run: |
          git clone -b ${{ needs.Set-Env.outputs.OPENSOURCE_BRANCH }} *************:${{ env.GITEE_USER }}/tapdata.git
          cd tapdata && git fetch --tags
      - name: Get Tapdata Version
        run: |
          cd tapdata && tapdata_version=$(git rev-parse --short HEAD) && cd ..
          mkdir -p version/${{ needs.Set-Env.outputs.TAG_NAME }}/
          echo "- tapdata: ${{ needs.Set-Env.outputs.OPENSOURCE_BRANCH }} $tapdata_version" > version/${{ needs.Set-Env.outputs.TAG_NAME }}/tapdata.version
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync -r --password-file=/tmp/rsync.passwd version/* rsync://root@*************:873/data/version/
      - name: Checkout Tapdata License
        run: |
          git clone -b ${{ needs.Set-Env.outputs.LISENSE_BRANCH }} *************:${{ env.GITEE_USER }}/tapdata-license.git
          cd tapdata-license && git fetch --tags
      - name: Get Tapdata License Version
        run: |
          cd tapdata-license && tapdata_license_version=$(git rev-parse --short HEAD) && cd ..
          mkdir -p version/${{ needs.Set-Env.outputs.TAG_NAME }}/
          echo "- tapdata-license: ${{ needs.Set-Env.outputs.LISENSE_BRANCH }} $tapdata_license_version" > version/${{ needs.Set-Env.outputs.TAG_NAME }}/tapdata-license.version
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync -r --password-file=/tmp/rsync.passwd version/* rsync://root@*************:873/data/version/
      - name: Checkout Tapdata Application
        run: |
          git clone -b ${{ env.TAPDATA_APPLICATION }} *************:${{ env.GITEE_USER }}/tapdata-application.git
      - name: Set Java Version
        run: |
          # 根据选择切换Java版本
          if [[ "${{ needs.Set-Env.outputs.JAVA_VERSION }}" == "java8" ]]; then
            sudo update-alternatives --set java /usr/java/jdk1.8.0_202/bin/java
            sudo update-alternatives --set javac /usr/java/jdk1.8.0_202/bin/javac
            echo "JAVA_HOME=/usr/java/jdk1.8.0_202" >> $GITHUB_ENV
            echo "PATH=/usr/java/jdk1.8.0_202/bin:$PATH" >> $GITHUB_ENV
            echo "已切换到Java 8"
          elif [[ "${{ needs.Set-Env.outputs.JAVA_VERSION }}" == "java11" ]]; then
            sudo update-alternatives --set java /usr/java/jdk-11.0.25/bin/java
            sudo update-alternatives --set javac /usr/java/jdk-11.0.25/bin/javac
            echo "JAVA_HOME=/usr/java/jdk-11.0.25" >> $GITHUB_ENV
            echo "PATH=/usr/java/jdk-11.0.25/bin:$PATH" >> $GITHUB_ENV
            echo "已切换到Java 11"
          elif [[ "${{ needs.Set-Env.outputs.JAVA_VERSION }}" == "java17" ]]; then
            sudo update-alternatives --set java /usr/java/jdk-17.0.12/bin/java
            sudo update-alternatives --set javac /usr/java/jdk-17.0.12/bin/javac
            echo "JAVA_HOME=/usr/java/jdk-17.0.12" >> $GITHUB_ENV
            echo "PATH=/usr/java/jdk-17.0.12/bin:$PATH" >> $GITHUB_ENV
            echo "已切换到Java 17"
          fi
          
          # 验证Java版本
          java -version
      - name: Patch Maven Dependencies
        run: |
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync --delete --password-file=/tmp/rsync.passwd -avz rsync://root@*************:873/data/enterprise-temp/tapdata/ /root/.m2/
      - name: Build Tapdata - And Analyze
        env:
          GITHUB_TOKEN: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        run: |
          cd tapdata-application && bash build/build.sh -c tapdata -u false -P true -l "-Dmaven.compile.fork=true -P idaas -P enterprise"
      - name: Build Tapdata Lisence
        run: |
          cd tapdata-application && bash build/build.sh -c tapdata-license -d true -P true -u false
      - name: Restore Java Version
        run: |
          # 直接切换回Java 8
          if [[ "${{ needs.Set-Env.outputs.JAVA_VERSION }}" != "java8" ]]; then
            echo "恢复到默认Java 8版本..."
            sudo update-alternatives --set java /usr/java/jdk1.8.0_202/bin/java
            sudo update-alternatives --set javac /usr/java/jdk1.8.0_202/bin/javac
            echo "JAVA_HOME=/usr/java/jdk1.8.0_202" >> $GITHUB_ENV
            echo "PATH=/usr/java/jdk1.8.0_202/bin:$PATH" >> $GITHUB_ENV
            echo "已恢复到Java 8"
            java -version
          else
            echo "当前已经是Java 8，无需恢复"
          fi
      - name: Upload Tapdata Component
        run: |
          cd tapdata-application && bash build/build.sh -k tapdata
          temp_dir=temp/${{ needs.Set-Env.outputs.TAG_NAME }}
          mkdir -p $temp_dir
          cp -r output/* $temp_dir/
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync -r --password-file=/tmp/rsync.passwd temp/ rsync://root@*************:873/data/temp/
          bash build/build.sh -d true
      - name: Upload Maven Dependencies
        run: |
          rm -rf /root/.m2/repository/com/tapdata
          rm -rf /root/.m2/repository/io/tapdata
          rsync --password-file=/tmp/rsync.passwd -rlDvz /root/.m2/ rsync://root@*************:873/data/enterprise-temp/tapdata/

  Build-Connectors:
    runs-on: office-build
    needs:
      - Set-Env
      - Push-Code-To-GOGS
    timeout-minutes: 60
    steps:
      - name: Clean Directory
        run: |
          tmpdir=$(mktemp -d)
          rsync --delete-before -a --force -r $tmpdir/ ./
          rm -rf $tmpdir
      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh
          echo "Host gitee.com" > ~/.ssh/config
          echo "  StrictHostKeyChecking no" >> ~/.ssh/config
          chmod 600 ~/.ssh/config
      - name: Checkout Tapdata Application
        run: |
          git clone -b ${{ env.TAPDATA_APPLICATION }} *************:${{ env.GITEE_USER }}/tapdata-application.git
      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh
          echo "Host gitee.com" > ~/.ssh/config
          echo "  StrictHostKeyChecking no" >> ~/.ssh/config
          chmod 600 ~/.ssh/config
      - name: Checkout Tapdata Code
        run: |
          git clone -b ${{ needs.Set-Env.outputs.OPENSOURCE_BRANCH }} *************:${{ env.GITEE_USER }}/tapdata.git
          cd tapdata && git fetch --tags
      - name: Get Connector Branch -- Manual
        if: ${{ needs.Set-Env.outputs.CONNECTORS_BRANCH != '' }}
        run: |
          connector_branch=${{ needs.Set-Env.outputs.CONNECTORS_BRANCH }}
          connector_branch_branch=${{ needs.Set-Env.outputs.CONNECTORS_ENTERPRISE_BRANCH }}
          echo "tapdata-connectors 分支：$connector_branch"
          echo "tapdata-connectors-enterprise 分支：$connector_branch_branch"
          echo "CONNECTOR_BRANCH=$connector_branch" >> $GITHUB_ENV
          echo "CONNECTOR_ENTERPRISE_BRANCH=$connector_branch_branch" >> $GITHUB_ENV
      - name: Get Connector Branch -- Auto Select
        if: ${{ needs.Set-Env.outputs.CONNECTORS_BRANCH == '' }}
        run: |
          cd tapdata
          opensource_branch=$(git rev-parse --abbrev-ref HEAD)
          echo "tapdata分支为：$opensource_branch"
          if [[ $opensource_branch == "release-v3.5.5" ]]; then
            connector_branch="release-v1.2.6"
          else
            connector_branch="main"
          fi
          echo "connectors分支为：$connector_branch"
          echo "CONNECTOR_BRANCH=$connector_branch" >> $GITHUB_ENV
          echo "CONNECTOR_ENTERPRISE_BRANCH=$connector_branch" >> $GITHUB_ENV
      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh
          echo "Host gitee.com" > ~/.ssh/config
          echo "  StrictHostKeyChecking no" >> ~/.ssh/config
          chmod 600 ~/.ssh/config
      - name: Checkout Connectors Code
        run: |
          git clone -b ${{ env.CONNECTOR_BRANCH }} *************:${{ env.GITEE_USER }}/tapdata-connectors.git
          cd tapdata-connectors && git fetch --tags
      - name: Get Connectors Version
        run: |
          cd tapdata-connectors && tapdata_connectors_version=$(git rev-parse --short HEAD) && cd ..
          mkdir -p version/${{ needs.Set-Env.outputs.TAG_NAME }}/
          echo "- tapdata-connectors: ${{ env.CONNECTOR_BRANCH }} $tapdata_connectors_version" > version/${{ needs.Set-Env.outputs.TAG_NAME }}/tapdata-connectors.version
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync -r --password-file=/tmp/rsync.passwd version/* rsync://root@*************:873/data/version/
      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh
          echo "Host gitee.com" > ~/.ssh/config
          echo "  StrictHostKeyChecking no" >> ~/.ssh/config
          chmod 600 ~/.ssh/config
      - name: Checkout Enterprise Connectors Code
        run: |
          git clone -b ${{ env.CONNECTOR_ENTERPRISE_BRANCH }} *************:${{ env.GITEE_USER }}/tapdata-connectors-enterprise.git
          cd tapdata-connectors-enterprise && git fetch --tags
      - name: Get Enterprise Connectors Version
        run: |
          cd tapdata-connectors-enterprise && tapdata_connectors_enterprise_version=$(git rev-parse --short HEAD) && cd ..
          mkdir -p version/${{ needs.Set-Env.outputs.TAG_NAME }}/
          echo "- tapdata-connectors-enterprise: ${{ env.CONNECTOR_ENTERPRISE_BRANCH }} $tapdata_connectors_enterprise_version" > version/${{ needs.Set-Env.outputs.TAG_NAME }}/tapdata-connectors-enterprise.version
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync -r --password-file=/tmp/rsync.passwd version/* rsync://root@*************:873/data/version/
      - name: Set Java Version
        run: |
          # 根据选择切换Java版本
          if [[ "${{ needs.Set-Env.outputs.JAVA_VERSION }}" == "java8" ]]; then
            sudo update-alternatives --set java /usr/java/jdk1.8.0_202/bin/java
            sudo update-alternatives --set javac /usr/java/jdk1.8.0_202/bin/javac
            echo "JAVA_HOME=/usr/java/jdk1.8.0_202" >> $GITHUB_ENV
            echo "PATH=/usr/java/jdk1.8.0_202/bin:$PATH" >> $GITHUB_ENV
            echo "已切换到Java 8"
          elif [[ "${{ needs.Set-Env.outputs.JAVA_VERSION }}" == "java11" ]]; then
            sudo update-alternatives --set java /usr/java/jdk-11.0.25/bin/java
            sudo update-alternatives --set javac /usr/java/jdk-11.0.25/bin/javac
            echo "JAVA_HOME=/usr/java/jdk-11.0.25" >> $GITHUB_ENV
            echo "PATH=/usr/java/jdk-11.0.25/bin:$PATH" >> $GITHUB_ENV
            echo "已切换到Java 11"
          elif [[ "${{ needs.Set-Env.outputs.JAVA_VERSION }}" == "java17" ]]; then
            sudo update-alternatives --set java /usr/java/jdk-17.0.12/bin/java
            sudo update-alternatives --set javac /usr/java/jdk-17.0.12/bin/javac
            echo "JAVA_HOME=/usr/java/jdk-17.0.12" >> $GITHUB_ENV
            echo "PATH=/usr/java/jdk-17.0.12/bin:$PATH" >> $GITHUB_ENV
            echo "已切换到Java 17"
          fi
          
          # 验证Java版本
          java -version
      - name: Patch Maven Dependencies
        run: |
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync --delete --password-file=/tmp/rsync.passwd -avz rsync://root@*************:873/data/enterprise-temp/tapdata/ /root/.m2/
      - name: Build Connectors
        run: |
          cd tapdata-application && bash build/build.sh -c opensource-connectors -u false -P true -l "-Dmaven.compile.fork=true"
      - name: Build Enterprise Connectors
        run: |
          cd tapdata-application && bash build/build.sh -c enterprise-connectors -u false -d true -P true -l "-Dmaven.compile.fork=true"
      - name: Restore Java Version
        run: |
          # 直接切换回Java 8
          if [[ "${{ needs.Set-Env.outputs.JAVA_VERSION }}" != "java8" ]]; then
            echo "恢复到默认Java 8版本..."
            sudo update-alternatives --set java /usr/java/jdk1.8.0_202/bin/java
            sudo update-alternatives --set javac /usr/java/jdk1.8.0_202/bin/javac
            echo "JAVA_HOME=/usr/java/jdk1.8.0_202" >> $GITHUB_ENV
            echo "PATH=/usr/java/jdk1.8.0_202/bin:$PATH" >> $GITHUB_ENV
            echo "已恢复到Java 8"
            java -version
          else
            echo "当前已经是Java 8，无需恢复"
          fi
      - name: Upload Tapdata Component
        run: |
          cd tapdata-application && bash build/build.sh -k connectors -i ${{ needs.Set-Env.outputs.CONNECTORS-OPTIONS }}
          temp_dir=temp/${{ needs.Set-Env.outputs.TAG_NAME }}
          mkdir -p $temp_dir
          cp -r output/* $temp_dir/
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync -r --password-file=/tmp/rsync.passwd temp/ rsync://root@*************:873/data/temp/
          bash build/build.sh -d true
      - name: Upload Maven Dependencies
        run: |
          rm -rf /root/.m2/repository/com/tapdata
          rm -rf /root/.m2/repository/io/tapdata
          rsync --password-file=/tmp/rsync.passwd -rlDvz /root/.m2/ rsync://root@*************:873/data/enterprise-temp/tapdata/

  Build-Tapdata-Enterprise:
    runs-on: office-build
    needs:
      - Set-Env
      - Push-Code-To-GOGS
    timeout-minutes: 60
    steps:
      - name: Clean Directory
        run: |
          tmpdir=$(mktemp -d)
          rsync --delete-before -a --force -r $tmpdir/ ./
          rm -rf $tmpdir
      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh
          echo "Host gitee.com" > ~/.ssh/config
          echo "  StrictHostKeyChecking no" >> ~/.ssh/config
          chmod 600 ~/.ssh/config
      - name: Checkout Tapdata Enterprise Code
        run: |
          git clone -b ${{ needs.Set-Env.outputs.ENTERPRISE_BRANCH }} *************:${{ env.GITEE_USER }}/tapdata-enterprise.git
          cd tapdata-enterprise && git fetch --tags
      - name: Get Tapdata-Enterprise Version
        run: |
          cd tapdata-enterprise && tapdata_enterprise_version=$(git rev-parse --short HEAD) && cd ..
          mkdir -p version/${{ needs.Set-Env.outputs.TAG_NAME }}/
          echo "- tapdata-enterprise: ${{ needs.Set-Env.outputs.ENTERPRISE_BRANCH }} $tapdata_enterprise_version" > version/${{ needs.Set-Env.outputs.TAG_NAME }}/tapdata-enterprise.version
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync -r --password-file=/tmp/rsync.passwd version/* rsync://root@*************:873/data/version/
      - name: Checkout Tapdata Application
        run: |
          git clone -b ${{ env.TAPDATA_APPLICATION }} *************:${{ env.GITEE_USER }}/tapdata-application.git
      - name: Set Java Version
        run: |
          # 根据选择切换Java版本
          if [[ "${{ needs.Set-Env.outputs.JAVA_VERSION }}" == "java8" ]]; then
            sudo update-alternatives --set java /usr/java/jdk1.8.0_202/bin/java
            sudo update-alternatives --set javac /usr/java/jdk1.8.0_202/bin/javac
            echo "JAVA_HOME=/usr/java/jdk1.8.0_202" >> $GITHUB_ENV
            echo "PATH=/usr/java/jdk1.8.0_202/bin:$PATH" >> $GITHUB_ENV
            echo "已切换到Java 8"
          elif [[ "${{ needs.Set-Env.outputs.JAVA_VERSION }}" == "java11" ]]; then
            sudo update-alternatives --set java /usr/java/jdk-11.0.25/bin/java
            sudo update-alternatives --set javac /usr/java/jdk-11.0.25/bin/javac
            echo "JAVA_HOME=/usr/java/jdk-11.0.25" >> $GITHUB_ENV
            echo "PATH=/usr/java/jdk-11.0.25/bin:$PATH" >> $GITHUB_ENV
            echo "已切换到Java 11"
          elif [[ "${{ needs.Set-Env.outputs.JAVA_VERSION }}" == "java17" ]]; then
            sudo update-alternatives --set java /usr/java/jdk-17.0.12/bin/java
            sudo update-alternatives --set javac /usr/java/jdk-17.0.12/bin/javac
            echo "JAVA_HOME=/usr/java/jdk-17.0.12" >> $GITHUB_ENV
            echo "PATH=/usr/java/jdk-17.0.12/bin:$PATH" >> $GITHUB_ENV
            echo "已切换到Java 17"
          fi
          
          # 验证Java版本
          java -version
      - name: Patch Dependencies
        run: |
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync --links --delete --password-file=/tmp/rsync.passwd -avz rsync://root@*************:873/data/enterprise-temp/tapdata-agent/ tapdata-enterprise/tapdata-agent/node_modules/
          if [[ "${{ needs.Set-Env.outputs.INCLUDE_APISERVER }}" == "true" ]]; then
            echo "包含ApiServer依赖..."
            rsync --links --delete --password-file=/tmp/rsync.passwd -avz rsync://root@*************:873/data/enterprise-temp/apiserver/ tapdata-enterprise/apiserver/node_modules/
          else
            echo "跳过ApiServer依赖..."
          fi
          mkdir -p ~/.pkg-cache/v3.4/
          rsync -r --password-file=/tmp/rsync.passwd rsync://root@*************:873/data/build-temp/ ~/.pkg-cache/v3.4/
      - name: Build Tapdata Enterprise
        run: |
          if [[ "${{ needs.Set-Env.outputs.INCLUDE_APISERVER }}" == "true" ]]; then
            cd tapdata-application && bash build/build.sh -c tapdata-enterprise -u false -v ${{ needs.Set-Env.outputs.TAPDATA_PLATFORM_VERSION }}
          else
            cd tapdata-application && bash build/build.sh -c tapdata-enterprise -u false -v ${{ needs.Set-Env.outputs.TAPDATA_PLATFORM_VERSION }} --exclude-apiserver
          fi
      - name: Restore Java Version
        run: |
          # 直接切换回Java 8
          if [[ "${{ needs.Set-Env.outputs.JAVA_VERSION }}" != "java8" ]]; then
            echo "恢复到默认Java 8版本..."
            sudo update-alternatives --set java /usr/java/jdk1.8.0_202/bin/java
            sudo update-alternatives --set javac /usr/java/jdk1.8.0_202/bin/javac
            echo "JAVA_HOME=/usr/java/jdk1.8.0_202" >> $GITHUB_ENV
            echo "PATH=/usr/java/jdk1.8.0_202/bin:$PATH" >> $GITHUB_ENV
            echo "已恢复到Java 8"
            java -version
          else
            echo "当前已经是Java 8，无需恢复"
          fi
      - name: Upload Tapdata Enterprise Component
        run: |
          if [[ "${{ needs.Set-Env.outputs.INCLUDE_APISERVER }}" == "false" ]]; then
            cd tapdata-application && bash build/build.sh -k tapdata-enterprise -v ${{ needs.Set-Env.outputs.TAPDATA_PLATFORM_VERSION }} --exclude-apiserver
          else
            cd tapdata-application && bash build/build.sh -k tapdata-enterprise -v ${{ needs.Set-Env.outputs.TAPDATA_PLATFORM_VERSION }}
          fi
          temp_dir=temp/${{ needs.Set-Env.outputs.TAG_NAME }}
          mkdir -p $temp_dir
          rsync -av --links output/ $temp_dir/
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync -r --links --password-file=/tmp/rsync.passwd temp/ rsync://root@*************:873/data/temp/
          bash build/build.sh -d true

  Build-Frontend:
    runs-on: office-build
    needs:
      - Set-Env
      - Push-Code-To-GOGS
    timeout-minutes: 60
    steps:
      - name: Clean Directory
        run: |
          tmpdir=$(mktemp -d)
          rsync --delete-before -a --force -r $tmpdir/ ./
          rm -rf $tmpdir
      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh
          echo "Host gitee.com" > ~/.ssh/config
          echo "  StrictHostKeyChecking no" >> ~/.ssh/config
          chmod 600 ~/.ssh/config
      - name: Checkout Frontend Code
        run: |
          git clone -b ${{ needs.Set-Env.outputs.FRONTEND_BRANCH }} *************:${{ env.GITEE_USER }}/tapdata-web.git
          cd tapdata-web && git fetch --tags
      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh
          echo "Host gitee.com" > ~/.ssh/config
          echo "  StrictHostKeyChecking no" >> ~/.ssh/config
          chmod 600 ~/.ssh/config
      - name: Checkout Docs Repo
        run: |
          rm -rf docs
          git clone -b enterprise-data-sources-docs *************:${{ env.GITEE_USER }}/docs.git
          cd docs && git fetch --tags
      - name: Get Frontend Version
        run: |
          cd tapdata-web && tapdata_web_version=$(git rev-parse --short HEAD) && cd ..
          mkdir -p version/${{ needs.Set-Env.outputs.TAG_NAME }}/
          echo "- tapdata-web: ${{ needs.Set-Env.outputs.FRONTEND_BRANCH }} $tapdata_web_version" > version/${{ needs.Set-Env.outputs.TAG_NAME }}/tapdata-web.version
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync -r --password-file=/tmp/rsync.passwd version/* rsync://root@*************:873/data/version/
      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh
          echo "Host gitee.com" > ~/.ssh/config
          echo "  StrictHostKeyChecking no" >> ~/.ssh/config
          chmod 600 ~/.ssh/config
      - name: Checkout Tapdata Application
        run: |
          git clone -b ${{ env.TAPDATA_APPLICATION }} *************:${{ env.GITEE_USER }}/tapdata-application.git
      - name: Build Frontend
        env:
          DAAS_BUILD_NUMBER: ${{ needs.Set-Env.outputs.TAG_NAME }}
        run: |
          cd tapdata-application && bash build/build.sh -c tapdata-enterprise-web -u false -m ${{ needs.Set-Env.outputs.FRONTEND_MODE }} -t ${{ needs.Set-Env.outputs.TAG_NAME }}
      - name: Build Docs
        run: |
          cd docs && npm install && npm run build:docs && cd ..
          mkdir -p tapdata-web/dist/docs/
          rsync -av docs/build/ tapdata-web/dist/docs/
      - name: Upload Tapdata Component
        run: |
          cd tapdata-application && bash build/build.sh -k web
          temp_dir=temp/${{ needs.Set-Env.outputs.TAG_NAME }}
          mkdir -p $temp_dir
          rsync -a output/ $temp_dir/
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync -r --password-file=/tmp/rsync.passwd temp/ rsync://root@*************:873/data/temp/
          bash build/build.sh -d true

  Make-Docker-Image:
    runs-on: office-build
    timeout-minutes: 60
    needs:
      - Build-Tapdata
      - Build-Connectors
      - Build-Frontend
      - Build-Tapdata-Enterprise
      - Set-Env
    permissions:
      contents: write
    steps:
      - name: Clean Directory
        run: |
          tmpdir=$(mktemp -d)
          rsync --delete-before -a --force -r $tmpdir/ ./
          rm -rf $tmpdir
      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh
          echo "Host gitee.com" > ~/.ssh/config
          echo "  StrictHostKeyChecking no" >> ~/.ssh/config
          chmod 600 ~/.ssh/config
      - name: Checkout Tapdata Application
        run: |
          git clone -b ${{ env.TAPDATA_APPLICATION }} *************:${{ env.GITEE_USER }}/tapdata-application.git
      - name: Package and Push Image
        if: ${{ needs.Set-Env.outputs.TAPDATA_PLATFORM_VERSION == 'Linux' }}
        run: |
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          cd tapdata-application
          mkdir -p output
          rsync -r --links --progress --password-file=/tmp/rsync.passwd rsync://root@*************:873/data/temp/${{ needs.Set-Env.outputs.TAG_NAME }}/ output/
          if [[ "${{ needs.Set-Env.outputs.INCLUDE_APISERVER }}" == "false" ]]; then
            bash build/build.sh -o image -t ${{ needs.Set-Env.outputs.TAG_NAME }} -m ${{ needs.Set-Env.outputs.FRONTEND_MODE }} --exclude-apiserver
          else
            bash build/build.sh -o image -t ${{ needs.Set-Env.outputs.TAG_NAME }} -m ${{ needs.Set-Env.outputs.FRONTEND_MODE }}
          fi
      - name: Print Image Info
        if: ${{ needs.Set-Env.outputs.TAPDATA_PLATFORM_VERSION == 'Linux' }}
        run: |
          echo '**Google Docker Image:**' >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY
          echo "harbor.internal.tapdata.io/tapdata/tapdata/tapdata-enterprise:${{ needs.Set-Env.outputs.TAG_NAME }}" >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY
      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh
          echo "Host gitee.com" > ~/.ssh/config
          echo "  StrictHostKeyChecking no" >> ~/.ssh/config
          chmod 600 ~/.ssh/config
      - name: Checkout Tapdata Application
        run: |
          rm -rf tapdata-application
          git clone -<NAME_EMAIL>:${{ env.GITEE_USER }}/tapdata-application.git
      - name: Add Commit ID and Version Map
        run: |
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync -r --progress --password-file=/tmp/rsync.passwd rsync://root@*************:873/data/version/${{ needs.Set-Env.outputs.TAG_NAME }}/ ./
          echo "## ${{ needs.Set-Env.outputs.TAG_NAME }}" >> tapdata-application/README.md
          cat tapdata.version >> tapdata-application/README.md
          cat tapdata-enterprise.version >> tapdata-application/README.md
          cat tapdata-web.version >> tapdata-application/README.md
          cat tapdata-license.version >> tapdata-application/README.md
          cat tapdata-connectors.version >> tapdata-application/README.md
          cat tapdata-connectors-enterprise.version >> tapdata-application/README.md
          cat tapdata-common-lib.version >> tapdata-application/README.md || exit 0
      - uses: stefanzweifel/git-auto-commit-action@v4
        with:
          commit_message: "Update README.md"
          branch: 'version'
          repository: 'tapdata-application'
          commit_user_name: 'cicd'
          commit_user_email: '<EMAIL>'

  Deploy-and-Test-Docker-Image:
    if: ${{ needs.Set-Env.outputs.RUN_TEST == 'true' }}
    runs-on: ubuntu-latest
    needs:
      - Make-Docker-Image
      - Set-Env
    steps:
      - name: Trigger - Deploy
        uses: convictional/trigger-workflow-and-wait@v1.6.1
        with:
          owner: tapdata
          repo: tapdata-application
          github_token: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
          workflow_file_name: deploy-tapdata-op.yaml
          ref: main
          wait_interval: 10
          client_payload: '{"TAG_NAME": "${{ needs.Set-Env.outputs.TAG_NAME }}", "AUTO_TEST": true, "JAVA_VERSION": "${{ needs.Set-Env.outputs.JAVA_VERSION }}"}'
          propagate_failure: true
          trigger_workflow: true
          wait_workflow: true
      - name: Checkout Tapdata Application
        uses: actions/checkout@v3
        with:
          repository: tapdata/tapdata-application
          token: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
          path: tapdata-application
          fetch-depth: 0
      - name: Get IP and Port
        run: |
          bash tapdata-application/build/upgrade.sh --get-ip-port=true --version=${{ needs.Set-Env.outputs.TAG_NAME }} --deploy-way=docker-compose
          IP=$(cat .service_ip_port | awk -F':' '{print $1}')
          PORT=$(cat .service_ip_port | awk -F':' '{print $2}')
          echo "IP=$IP" >> $GITHUB_ENV
          echo "PORT=$PORT" >> $GITHUB_ENV
      - name: Run Test
        uses: convictional/trigger-workflow-and-wait@v1.6.5
        with:
          owner: tapdata
          repo: tapce
          github_token: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
          workflow_file_name: run_tapce.yaml
          ref: master
          wait_interval: 60
          client_payload: '{"PROPERTIES_FILE_NAME": "default", "JAVA_VERSION": "${{ inputs.JAVA_VERSION }}", "OPTIONS": "-Denv.host=${{ env.IP }} -Denv.web.port=${{ env.PORT }} -Denv.api.port=${{ env.PORT }}"}'
          propagate_failure: true
          trigger_workflow: true
          wait_workflow: true

  Make-Tar-File:
    runs-on: office-build
    timeout-minutes: 60
    needs:
      - Build-Tapdata
      - Build-Connectors
      - Build-Frontend
      - Build-Tapdata-Enterprise
      - Set-Env
    permissions:
      contents: write
    if: ${{ needs.Set-Env.outputs.IS_TAR == 'true' }}
    steps:
      - name: Clean Directory
        run: |
          tmpdir=$(mktemp -d)
          rsync --delete-before -a --force -r $tmpdir/ ./
          rm -rf $tmpdir
      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh
          echo "Host gitee.com" > ~/.ssh/config
          echo "  StrictHostKeyChecking no" >> ~/.ssh/config
          chmod 600 ~/.ssh/config
      - name: Checkout Tapdata Application
        run: |
          git clone -b ${{ env.TAPDATA_APPLICATION }} *************:${{ env.GITEE_USER }}/tapdata-application.git
      - name: Package and Push Tar File
        run: |
          cd tapdata-application
          mkdir -p output
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync -r --links --progress --password-file=/tmp/rsync.passwd rsync://root@*************:873/data/temp/${{ needs.Set-Env.outputs.TAG_NAME }}/ output/
          if [[ '${{ needs.Set-Env.outputs.FRONTEND_MODE }}' == 'oem' ]]; then
            rsync -v --links --progress --password-file=/tmp/rsync.passwd rsync://root@*************:873/data/enterprise-artifact/tools/service_control output/
          fi
          if [[ "${{ needs.Set-Env.outputs.INCLUDE_APISERVER }}" == "false" ]]; then
            bash build/build.sh -o tar -t ${{ needs.Set-Env.outputs.TAG_NAME }} -m ${{ needs.Set-Env.outputs.FRONTEND_MODE }} --exclude-apiserver
          else
            bash build/build.sh -o tar -t ${{ needs.Set-Env.outputs.TAG_NAME }} -m ${{ needs.Set-Env.outputs.FRONTEND_MODE }}
          fi
      - name: Print Tar Info
        run: |
          file_name=${{ needs.Set-Env.outputs.FRONTEND_MODE }}-tapdata-enterprise-${{ needs.Set-Env.outputs.TAG_NAME }}.tar.gz
          echo '**Download Tar File:**' >> $GITHUB_STEP_SUMMARY
          echo "[Download Link](http://58.251.34.123:5244/gz/$file_name)" >> $GITHUB_STEP_SUMMARY
          echo "username/password： **admin/Gotapd8!**" >> $GITHUB_STEP_SUMMARY

  Clean-Build-Temp:
    runs-on: office-build
    timeout-minutes: 60
    needs:
      - Deploy-and-Test-Docker-Image
      - Make-Tar-File
      - Set-Env
    if: ${{ always() }}
    steps:
      - name: Clean Build Temp
        run: |
          mkdir -p temp/${{ needs.Set-Env.outputs.TAG_NAME }}/
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync -r --delete --password-file=/tmp/rsync.passwd temp/${{ needs.Set-Env.outputs.TAG_NAME }}/ rsync://root@*************:873/data/temp/${{ needs.Set-Env.outputs.TAG_NAME }}/
      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh
          echo "Host gitee.com" > ~/.ssh/config
          echo "  StrictHostKeyChecking no" >> ~/.ssh/config
          chmod 600 ~/.ssh/config
      - name: Checkout Tapdata Application
        run: |
          rm -rf tapdata-application
          git clone -<NAME_EMAIL>:${{ env.GITEE_USER }}/tapdata-application.git
      - name: Close Test Env
        if: ${{ needs.Deploy-and-Test-Docker-Image.result == 'success' }}
        run: |
          bash tapdata-application/build/upgrade.sh --delete-env=${{ needs.Set-Env.outputs.TAG_NAME }} --delete-env-tag=true
