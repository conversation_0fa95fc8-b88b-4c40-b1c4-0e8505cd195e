name: Upgrade Cloud Tapdata

on:
  workflow_dispatch:
    inputs:
      PARAMS_AS_JSON:
        description: 'Upgrade Params'
        required: true
        type: string
        default: "{}"

env:
  TAPDATA_APPLICATION: main
  JSON_INPUT: ${{ inputs.PARAMS_AS_JSON }}
  GITEE_USER: tapdata_1

jobs:

  Set-Tag:
    runs-on: ubuntu-latest
    outputs:
      tag: ${{ steps.set-outputs.outputs.tag }}
      FRONTEND_BRANCH: ${{ steps.set-outputs.outputs.FRONTEND_BRANCH }}
      TAPFLOW_BRANCH: ${{ steps.set-outputs.outputs.TAPFLOW_BRANCH }}
      OPENSOURCE_BRANCH: ${{ steps.set-outputs.outputs.OPENSOURCE_BRANCH }}
      ENTERPRISE_BRANCH: ${{ steps.set-outputs.outputs.ENTERPRISE_BRANCH }}
      DFS_BRANCH: ${{ steps.set-outputs.outputs.DFS_BRANCH }}
      BUILD-FRONTEND: ${{ steps.set-outputs.outputs.BUILD-FRONTEND }}
      BUILD-TCM: ${{ steps.set-outputs.outputs.BUILD-TCM }}
      BUILD-TM-JAVA: ${{ steps.set-outputs.outputs.BUILD-TM-JAVA }}
      BUILD-AGENT: ${{ steps.set-outputs.outputs.BUILD-AGENT }}
      BUILD-TICKET: ${{ steps.set-outputs.outputs.BUILD-TICKET }}
      BUILD-TAPFLOW: ${{ steps.set-outputs.outputs.BUILD-TAPFLOW }}
      ENV: ${{ steps.set-outputs.outputs.ENV }}
    steps:
      - name: Set Variables from Json
        run: |
          echo -e "DFS_BRANCH=$(echo $JSON_INPUT | jq -r .DFS_BRANCH)" >> $GITHUB_ENV
          echo -e "JOB_ID=$(echo $JSON_INPUT | jq -r .JOB_ID)" >> $GITHUB_ENV
      - name: ${{ env.JOB_ID }}
        run: |
          echo ${{ env.JOB_ID }}
      - name: Checkout Tapdata-Cloud Code
        uses: actions/checkout@v3
        with:
          repository: tapdata/tapdata-cloud
          ref: ${{ env.DFS_BRANCH }}
          fetch-depth: 0
          token: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
          path: tapdata-cloud
      - name: Checkout Tapdata-Application Code
        uses: actions/checkout@v3
        with:
          repository: tapdata/tapdata-application
          ref: ${{ env.TAPDATA_APPLICATION }}
          fetch-depth: 0
          token: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
          path: tapdata-application
      - name: Set Tag
        id: set-outputs
        run: |
          tag=$(cd tapdata-application && bash ./build/cloud.sh --action=set-tag)
          echo ::set-output name=tag::$tag
          echo "tag=$tag" >> $GITHUB_ENV
          echo ::set-output name=FRONTEND_BRANCH::$(echo $JSON_INPUT | jq -r ".FRONTEND_BRANCH")
          echo ::set-output name=TAPFLOW_BRANCH::$(echo $JSON_INPUT | jq -r ".TAPFLOW_BRANCH")
          echo ::set-output name=OPENSOURCE_BRANCH::$(echo $JSON_INPUT | jq -r ".OPENSOURCE_BRANCH")
          echo ::set-output name=ENTERPRISE_BRANCH::$(echo $JSON_INPUT | jq -r ".ENTERPRISE_BRANCH")
          echo ::set-output name=DFS_BRANCH::$(echo $JSON_INPUT | jq -r ".DFS_BRANCH")
          echo ::set-output name=BUILD-FRONTEND::$(echo $JSON_INPUT | jq -r '.["BUILD-FRONTEND"]')
          echo ::set-output name=BUILD-TCM::$(echo $JSON_INPUT | jq -r '.["BUILD-TCM"]')
          echo ::set-output name=BUILD-TM-JAVA::$(echo $JSON_INPUT | jq -r '.["BUILD-TM-JAVA"]')
          echo ::set-output name=BUILD-AGENT::$(echo $JSON_INPUT | jq -r '.["BUILD-AGENT"]')
          echo ::set-output name=BUILD-TICKET::$(echo $JSON_INPUT | jq -r '.["BUILD-TICKET"]')
          echo ::set-output name=BUILD-TAPFLOW::$(echo $JSON_INPUT | jq -r '.["BUILD-TAPFLOW"]')
          echo ::set-output name=ENV::$(echo $JSON_INPUT | jq -r ".ENV")
      - name: Tag=${{ env.tag }};
        run: |
          echo "success"

  Sync-Tapdata-Cloud-Code:
    uses: ./.github/workflows/sync-code-to-office.yaml
    needs:
      - Set-Tag
    secrets: inherit
    with:
      tapdata-application: main
      tapdata: ${{ needs.Set-Tag.outputs.OPENSOURCE_BRANCH }}
      tapdata-cloud: ${{ needs.Set-Tag.outputs.DFS_BRANCH }}
      tapdata-enterprise: ${{ needs.Set-Tag.outputs.ENTERPRISE_BRANCH }}
      tapdata-enterprise-web: ${{ needs.Set-Tag.outputs.FRONTEND_BRANCH }}

  Upgrade-Frontend:
    needs:
      - Sync-Tapdata-Cloud-Code
      - Set-Tag
    runs-on: cloud-runner
    if: ${{ needs.Set-Tag.outputs.BUILD-FRONTEND == 'true' }}
    steps:
      - name: Clean Workspace
        run: |
          rm -rf ./*
      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh
          echo "Host gitee.com" > ~/.ssh/config
          echo "  StrictHostKeyChecking no" >> ~/.ssh/config
          chmod 600 ~/.ssh/config
      - name: Checkout Tapdata-Web Code
        run: |
          git clone -b ${{ needs.Set-Tag.outputs.FRONTEND_BRANCH }} *************:${{ env.GITEE_USER }}/tapdata-web.git tapdata-web
          cd tapdata-web && git fetch --tags
      - name: Checkout Tapdata-Cloud Code
        run: |
          git clone -b ${{ needs.Set-Tag.outputs.DFS_BRANCH }} *************:${{ env.GITEE_USER }}/tapdata-cloud.git
          cd tapdata-cloud && git fetch --tags
      - name: Checkout Tapdata-Application Code
        run: |
          git clone -b ${{ env.TAPDATA_APPLICATION }} *************:${{ env.GITEE_USER }}/tapdata-application.git
          cd tapdata-application && git fetch --tags
      - name: Build And Update Frontend
        uses: nick-fields/retry@v2
        with:
          max_attempts: 3
          retry_on: error
          timeout_minutes: 60
          command: |
            bash tapdata-application/build/cloud.sh --action=update --component=console --tag=${{ needs.Set-Tag.outputs.tag }} --env=${{ needs.Set-Tag.outputs.ENV }}

  Sync-TapFlow-Code:
    runs-on: ubuntu-latest
    steps:
      - name: Configure hosts file
        run: |
          echo "********** gitee.com" | sudo tee -a /etc/hosts
      - id: sync
        continue-on-error: true
        uses: wearerequired/git-mirror-action@v1
        env:
          SSH_PRIVATE_KEY: ${{ secrets.GITEE_SSH_KEY }}
          SSH_KNOWN_HOSTS: ${{ secrets.GITEE_KNOWN_HOSTS }}
        with:
          source-repo: "**************:tapdata/tapflow-web.git"
          destination-repo: "*************:tapdata_1/tapflow-web.git"
      - name: Clean workspace before retry 1
        if: steps.sync.outcome == 'failure'
        run: |
          sudo chmod -R 777 . || true
          sudo rm -rf *
      - name: Retry 1
        if: steps.sync.outcome == 'failure'
        continue-on-error: true
        uses: wearerequired/git-mirror-action@v1
        env:
          SSH_PRIVATE_KEY: ${{ secrets.GITEE_SSH_KEY }}
          SSH_KNOWN_HOSTS: ${{ secrets.GITEE_KNOWN_HOSTS }}
        with:
          source-repo: "**************:tapdata/tapflow-web.git"
          destination-repo: "*************:tapdata_1/tapflow-web.git"
      - name: Clean workspace before retry 2
        if: steps.sync.outcome == 'failure'
        run: |
          sudo chmod -R 777 . || true
          sudo rm -rf *
      - name: Retry 2
        if: steps.sync.outcome == 'failure'
        uses: wearerequired/git-mirror-action@v1
        env:
          SSH_PRIVATE_KEY: ${{ secrets.GITEE_SSH_KEY }}
          SSH_KNOWN_HOSTS: ${{ secrets.GITEE_KNOWN_HOSTS }}
        with:
          source-repo: "**************:tapdata/tapflow-web.git"
          destination-repo: "*************:tapdata_1/tapflow-web.git"

  Upgrade-Tapflow:
    needs:
      - Sync-Tapdata-Cloud-Code
      - Set-Tag
      - Sync-TapFlow-Code
    runs-on: cloud-runner
    if: ${{ needs.Set-Tag.outputs.BUILD-TAPFLOW == 'true' }}
    steps:
      - name: Clean Workspace
        run: |
          rm -rf ./*
      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh
          echo "Host gitee.com" > ~/.ssh/config
          echo "  StrictHostKeyChecking no" >> ~/.ssh/config
          chmod 600 ~/.ssh/config
      - name: Checkout Tapdata-Flow Code
        run: |
          git clone -b ${{ needs.Set-Tag.outputs.TAPFLOW_BRANCH }} *************:${{ env.GITEE_USER }}/tapflow-web.git
          cd tapflow-web && git fetch --tags
      - name: Checkout Tapdata-Cloud Code
        run: |
          git clone -b ${{ needs.Set-Tag.outputs.DFS_BRANCH }} *************:${{ env.GITEE_USER }}/tapdata-cloud.git
          cd tapdata-cloud && git fetch --tags
      - name: Checkout Tapdata-Application Code
        run: |
          git clone -b ${{ env.TAPDATA_APPLICATION }} *************:${{ env.GITEE_USER }}/tapdata-application.git
          cd tapdata-application && git fetch --tags
      - name: Build And Update Tapflow
        run: |
          source ~/.nvm/nvm.sh
          nvm use 18 || nvm install 18 && nvm use 18
          npm config set registry https://registry.npmmirror.com
          bash tapdata-application/build/cloud.sh --action=update --component=tapflow --tag=${{ needs.Set-Tag.outputs.tag }} --env=${{ needs.Set-Tag.outputs.ENV }}
          nvm use 16 || nvm install 16 && nvm use 16

  Upgrade-Tcm:
    needs:
      - Sync-Tapdata-Cloud-Code
      - Set-Tag
    runs-on: cloud-runner
    if: ${{ needs.Set-Tag.outputs.BUILD-TCM == 'true' }}
    steps:
      - name: Clean Workspace
        run: |
          rm -rf ./*
      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh
          echo "Host gitee.com" > ~/.ssh/config
          echo "  StrictHostKeyChecking no" >> ~/.ssh/config
          chmod 600 ~/.ssh/config
      - name: Checkout Tapdata-Cloud Code
        run: |
          git clone -b ${{ needs.Set-Tag.outputs.DFS_BRANCH }} *************:${{ env.GITEE_USER }}/tapdata-cloud.git
          cd tapdata-cloud && git fetch --tags
      - name: Checkout Tapdata-Application Code
        run: |
          git clone -b ${{ env.TAPDATA_APPLICATION }} *************:${{ env.GITEE_USER }}/tapdata-application.git
          cd tapdata-application && git fetch --tags
      - name: Patch Dependency
        run: |
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync --delete --password-file=/tmp/rsync.passwd -avz rsync://root@*************:873/data/enterprise-temp/tapdata/ /root/.m2/
      - name: Build And Update Tcm
        run: |
          bash tapdata-application/build/cloud.sh --action=update --component=tcm --tag=${{ needs.Set-Tag.outputs.tag }} --env=${{ needs.Set-Tag.outputs.ENV }}

  Upgrade-Tm-Java:
    needs:
      - Sync-Tapdata-Cloud-Code
      - Set-Tag
    runs-on: cloud-runner
    if: ${{ needs.Set-Tag.outputs.BUILD-TM-JAVA == 'true' }}
    steps:
      - name: Clean Workspace
        run: |
          rm -rf ./*
      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh
          echo "Host gitee.com" > ~/.ssh/config
          echo "  StrictHostKeyChecking no" >> ~/.ssh/config
          chmod 600 ~/.ssh/config
      - name: Checkout Tapdata-Enterprise Code
        run: |
          git clone -b ${{ needs.Set-Tag.outputs.ENTERPRISE_BRANCH }} *************:${{ env.GITEE_USER }}/tapdata-enterprise.git
          cd tapdata-enterprise && git fetch --tags
      - name: Checkout Tapdata-Cloud Code
        run: |
          git clone -b ${{ needs.Set-Tag.outputs.DFS_BRANCH }} *************:${{ env.GITEE_USER }}/tapdata-cloud.git
          cd tapdata-cloud && git fetch --tags
      - name: Checkout Tapdata Code
        run: |
          git clone -b ${{ needs.Set-Tag.outputs.OPENSOURCE_BRANCH }} *************:${{ env.GITEE_USER }}/tapdata.git
          cd tapdata && git fetch --tags
      - name: Checkout Tapdata-Application Code
        run: |
          git clone -b ${{ env.TAPDATA_APPLICATION }} *************:${{ env.GITEE_USER }}/tapdata-application.git
          cd tapdata-application && git fetch --tags
      - name: Patch Dependency
        run: |
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync --delete --password-file=/tmp/rsync.passwd -avz rsync://root@*************:873/data/enterprise-temp/tapdata/ /root/.m2/
      - name: Build And Update Tm-Java
        run: |
          bash tapdata-application/build/cloud.sh --action=update --component=tm-java --tag=${{ needs.Set-Tag.outputs.tag }} --env=${{ needs.Set-Tag.outputs.ENV }}

  Upgrade-Ticket:
    needs:
      - Sync-Tapdata-Cloud-Code
      - Set-Tag
    runs-on: cloud-runner
    if: ${{ needs.Set-Tag.outputs.BUILD-TICKET == 'true' }}
    steps:
      - name: Clean Workspace
        run: |
          rm -rf ./*
      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh
          echo "Host gitee.com" > ~/.ssh/config
          echo "  StrictHostKeyChecking no" >> ~/.ssh/config
          chmod 600 ~/.ssh/config
      - name: Checkout Tapdata-Cloud Code
        run: |
          git clone -b ${{ needs.Set-Tag.outputs.DFS_BRANCH }} *************:${{ env.GITEE_USER }}/tapdata-cloud.git
          cd tapdata-cloud && git fetch --tags
      - name: Checkout Tapdata-Enterprise Code
        run: |
          git clone -b ${{ needs.Set-Tag.outputs.ENTERPRISE_BRANCH }} *************:${{ env.GITEE_USER }}/tapdata-enterprise.git
          cd tapdata-enterprise && git fetch --tags
      - name: Checkout Tapdata-Application Code
        run: |
          git clone -b ${{ env.TAPDATA_APPLICATION }} *************:${{ env.GITEE_USER }}/tapdata-application.git
          cd tapdata-application && git fetch --tags
      - name: Build And Update Ticket
        run: |
          bash tapdata-application/build/cloud.sh --action=update --component=ticket --tag=${{ needs.Set-Tag.outputs.tag }} --env=${{ needs.Set-Tag.outputs.ENV }}

  Update-Agent:
    needs:
      - Sync-Tapdata-Cloud-Code
      - Set-Tag
    runs-on: cloud-runner
    if: ${{ needs.Set-Tag.outputs.BUILD-AGENT == 'true' }}
    steps:
      - name: Clean Workspace
        run: |
          rm -rf ./*
      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh
          echo "Host gitee.com" > ~/.ssh/config
          echo "  StrictHostKeyChecking no" >> ~/.ssh/config
          chmod 600 ~/.ssh/config
      - name: Checkout Tapdata-Enterprise Code
        run: |
          git clone -b ${{ needs.Set-Tag.outputs.ENTERPRISE_BRANCH }} *************:${{ env.GITEE_USER }}/tapdata-enterprise.git
          cd tapdata-enterprise && git fetch --tags
      - name: Checkout Tapdata-Cloud Code
        run: |
          git clone -b ${{ needs.Set-Tag.outputs.DFS_BRANCH }} *************:${{ env.GITEE_USER }}/tapdata-cloud.git
          cd tapdata-cloud && git fetch --tags
      - name: Checkout Tapdata Code
        run: |
          git clone -b ${{ needs.Set-Tag.outputs.OPENSOURCE_BRANCH }} *************:${{ env.GITEE_USER }}/tapdata.git
          cd tapdata && git fetch --tags
      - name: Checkout Tapdata-Application Code
        run: |
          git clone -b ${{ env.TAPDATA_APPLICATION }} *************:${{ env.GITEE_USER }}/tapdata-application.git
          cd tapdata-application && git fetch --tags
      - name: Patch Dependency
        run: |
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync --delete --password-file=/tmp/rsync.passwd -avz rsync://root@*************:873/data/enterprise-temp/tapdata/ /root/.m2/
          npm config set registry https://registry.npmmirror.com
          npm install -g pkg
          mkdir -p /root/.pkg-cache/v3.4/
          rsync --delete --password-file=/tmp/rsync.passwd -vzrt rsync://root@*************:873/data/build-temp/ /root/.pkg-cache/v3.4/
      - name: Build And Update Agent
        run: |
          bash tapdata-application/build/cloud.sh --action=update --component=agent,tapdata_agent --tag=${{ needs.Set-Tag.outputs.tag }} --env=${{ needs.Set-Tag.outputs.ENV }}
