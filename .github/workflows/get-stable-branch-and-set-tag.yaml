name: Get Stable Branch and Set Tag

on:
  workflow_call:
    inputs:
      tapdata-branch:
        description: 'Tapdata branch'
        required: false
        type: string
        default: 'main'
      tapdata-enterprise-branch:
        description: 'Tapdata enterprise branch'
        required: false
        type: string
        default: 'main'
      tapdata-frontend-branch:
        description: 'Tapdata frontend branch'
        required: false
        type: string
        default: 'main'
      connectors-branch:
        description: 'Tapdata connectors branch'
        required: false
        type: string
        default: 'main'
      license-branch:
        description: 'Tapdata connectors branch'
        required: false
        type: string
        default: 'main'
    secrets:
      TAPDATA_ENT_CICD_TOKEN:
        description: 'Tapdata enterprise cicd token'
        required: true
    outputs:
      OPENSOURCE_BRANCH:
        description: 'Opensource branch'
        value: ${{ jobs.Get-Stable-Branch-and-Set-Tag.outputs.OPENSOURCE_BRANCH }}
      ENTERPRISE_BRANCH:
        description: 'Enterprise branch'
        value: ${{ jobs.Get-Stable-Branch-and-Set-Tag.outputs.ENTERPRISE_BRANCH }}
      FRONTEND_BRANCH:
        description: 'Frontend branch'
        value: ${{ jobs.Get-Stable-Branch-and-Set-Tag.outputs.FRONTEND_BRANCH }}
      TAG_NAME:
        description: 'Tag name'
        value: ${{ jobs.Get-Stable-Branch-and-Set-Tag.outputs.TAG_NAME }}
      CONNECTORS_BRANCH:
        description: 'Connectors branch'
        value: ${{ jobs.Get-Stable-Branch-and-Set-Tag.outputs.CONNECTORS_BRANCH }}
      LICENSE_BRANCH:
        description: 'License branch'
        value: ${{ jobs.Get-Stable-Branch-and-Set-Tag.outputs.LICENSE_BRANCH }}

jobs:

  Get-Stable-Branch-and-Set-Tag:
    runs-on: ubuntu-latest
    outputs:
      OPENSOURCE_BRANCH: ${{ steps.set-output.outputs.OPENSOURCE_BRANCH }}
      ENTERPRISE_BRANCH: ${{ steps.set-output.outputs.ENTERPRISE_BRANCH }}
      FRONTEND_BRANCH: ${{ steps.set-output.outputs.FRONTEND_BRANCH }}
      CONNECTORS_BRANCH: ${{ steps.set-output.outputs.CONNECTORS_BRANCH }}
      LICENSE_BRANCH: ${{ steps.set-output.outputs.LICENSE_BRANCH }}
      TAG_NAME: ${{ steps.set-output.outputs.TAG_NAME }}
    steps:
      - name: Checkout Tapdata Opensource
        uses: actions/checkout@v3
        with:
          repository: 'tapdata/tapdata'
          token: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
          ref: ${{ inputs.tapdata-branch }}
          path: tapdata
          fetch-depth: 0
      - name: Set Tag
        run: |
          cd tapdata
          main_tag=$(git describe --tags | cut -d '-' -f 1)
          current_timestamp=$(date +%s)
          hex_timestamp=$(printf "%X" "$current_timestamp" | tr 'A-F' 'a-f')
          tag_name="$main_tag-$hex_timestamp"
          echo "TAG_NAME=$tag_name" >> $GITHUB_ENV
      - name: Get last stable branch
        id: set-output
        run: |
          OPENSOURCE_BRANCH=${{ inputs.tapdata-branch }}
          ENTERPRISE_BRANCH=${{ inputs.tapdata-enterprise-branch }}
          CONNECTORS_BRANCH=${{ inputs.connectors-branch }}
          LICENSE_BRANCH=${{ inputs.license-branch }}
          FRONTEND_BRANCH=${{ inputs.tapdata-frontend-branch }}
          echo "::set-output name=OPENSOURCE_BRANCH::${OPENSOURCE_BRANCH}"
          echo "::set-output name=ENTERPRISE_BRANCH::${ENTERPRISE_BRANCH}"
          echo "::set-output name=FRONTEND_BRANCH::${FRONTEND_BRANCH}"
          echo "::set-output name=CONNECTORS_BRANCH::${CONNECTORS_BRANCH}"
          echo "::set-output name=LICENSE_BRANCH::${LICENSE_BRANCH}"
          echo "::set-output name=TAG_NAME::${TAG_NAME}"