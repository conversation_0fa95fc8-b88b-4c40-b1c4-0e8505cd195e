name: TestSigma Test

on:
  workflow_dispatch:
    inputs:
      IP:
        description: 'IP'
        required: true
        type: string
      PORT:
        description: 'PORT'
        required: true
        type: string
      Action:
        description: 'Action (difference plan)'
        required: true
        default: "workflow_dispatch"
        type: choice
        options:
          - "workflow_dispatch"
          - "schedule"

env:
  GITEE_USER: tapdata_1

jobs:

  TestSigma-Test:
    runs-on: office-build
    concurrency:
      group: staging_environment
    timeout-minutes: 120
    steps:
      - name: Clean Workspace
        run: |
          rm -rf ./*
      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh
          echo "Host gitee.com" > ~/.ssh/config
          echo "  StrictHostKeyChecking no" >> ~/.ssh/config
          chmod 600 ~/.ssh/config
      - name: Fetch Code - Main Repo
        run: |
          git clone -<NAME_EMAIL>:${{ env.GITEE_USER }}/tapdata.git
          cd tapdata && git fetch --tags
      - name: Compile - IEngine And Management
        run: |
          #mvn clean install -DskipTests -P idaas -P not_encrypt -U -T1C
          echo "OK"
      - name: Run - Local Service
        run: |
          cd tapdata && bash build/run_smoke_tests.sh --runtimedata="url=http://${{ inputs.IP }}:${{ inputs.PORT }}" --maxtimeinmins=120 --testplanid=724 --pollcount=12
      - name: Print Test Result
        if: ${{ always() }}
        run: |
          cd tapdata
          run_id=`cat ./run_id`
          test_plan_id=`cat ./test_plan_id`
          echo "**TestSigma Test Result Link:**" >> $GITHUB_STEP_SUMMARY
          echo "https://app.testsigma.com/ui/td/results/$test_plan_id/runs/$run_id" >> $GITHUB_STEP_SUMMARY
      - name: Test - Register Test Connector
        run: |
          echo "OK"
      - name: Test - E2E Qucik Test
        run: |
          echo "OK"