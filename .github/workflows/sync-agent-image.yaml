name: Sync Cloud Agent Image

on:
  workflow_dispatch:
    inputs:
      AGENT_VERSION:
        description: 'Agent版本'
        required: true
        type: string
      JOB_ID:
        description: 'Job ID'
        required: false
        type: string

env:
  GITEE_USER: tapdata_1

jobs:

  Sync-Tapdata-Cloud-Code:
    uses: ./.github/workflows/sync-code-to-office.yaml
    secrets: inherit
    with:
      tapdata-application: main

  Sync-Agent-Image:
    runs-on: cloud-runner
    needs:
      - Sync-Tapdata-Cloud-Code
    timeout-minutes: 15
    steps:
      - name: ${{ inputs.JOB_ID }}
        run: |
          echo "AGENT_VERSION=${{ inputs.AGENT_VERSION }}" >> $GITHUB_ENV
      - name: Clean Workdir
        run: |
          rm -rf ./*
      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh
          echo "Host gitee.com" > ~/.ssh/config
          echo "  StrictHostKeyChecking no" >> ~/.ssh/config
          chmod 600 ~/.ssh/config
      - name: Checkout Tapdata Application Code
        run: |
          git clone -<NAME_EMAIL>:${{ env.GITEE_USER }}/tapdata-application.git
          cd tapdata-application && git fetch --tags
      - name: Sync Agent Image
        uses: nick-fields/retry@v2
        with:
          timeout_minutes: 30
          max_attempts: 3
          command: |
            cd tapdata-application && bash build/upgrade.sh --sync-agent-image=true --version=${{ inputs.AGENT_VERSION }}
