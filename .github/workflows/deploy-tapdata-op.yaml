name: Deploy Tapdata

on:
  workflow_dispatch:
    inputs:
      TAG_NAME:
        description: 'Tag name'
        required: true
        type: string
      AUTO_TEST:
        description: 'Auto test'
        required: true
        type: boolean
        default: false
      DEPLOY:
        description: 'Deploy to Env: '
        required: false
        default: "无"
        type: choice
        options:
          - "无"
          - "dev(3030)"
          - "new(3031)"
          - "cicd(3032)"
      JAVA_VERSION:
        description: 'Java version'
        required: true
        type: choice
        default: "java17"
        options:
          - "java8"
          - "java11"
          - "java17"

env:
  TAPDATA_APPLICATION: main
  GITEE_USER: tapdata_1

jobs:
  Deploy-Tapdata:
    runs-on: office-build
    timeout-minutes: 60
    steps:
      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh
          echo "Host gitee.com" > ~/.ssh/config
          echo "  StrictHostKeyChecking no" >> ~/.ssh/config
          chmod 600 ~/.ssh/config
      - name: Checkout Tapdata Application
        run: |
          rm -rf tapdata-application
          git clone -b ${{ env.TAPDATA_APPLICATION }} *************:${{ env.GITEE_USER }}/tapdata-application.git
      - name: Deploy Auto Test Env
        if: ${{ inputs.AUTO_TEST }}
        run: |
          cd tapdata-application
          bash build/upgrade.sh --deploy=true --version=${{ inputs.TAG_NAME }} --deploy-way=docker-compose --java-version=${{ inputs.JAVA_VERSION }}
      - name: Deploy
        if: ${{ inputs.DEPLOY != '' && inputs.DEPLOY != '无' }}
        run: |
          cd tapdata-application
          if [[ '${{ inputs.DEPLOY }}' == 'dev(3030)' ]]; then
            upgrade_env='tapdata-dev'
          elif [[ '${{ inputs.DEPLOY }}' == 'new(3031)' ]]; then
            upgrade_env='tapdata-new'
          elif [[ '${{ inputs.DEPLOY }}' == 'cicd(3032)' ]]; then
            upgrade_env='tapdata-cicd'
          fi
          bash build/upgrade.sh --upgrade=true --version=${{ inputs.TAG_NAME }} --upgrade-env=$upgrade_env --deploy-way=docker-compose --java-version=${{ inputs.JAVA_VERSION }}