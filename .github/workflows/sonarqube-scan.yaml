name: SonarQube Scan

on:
  workflow_dispatch:
    inputs:
      tapdata:
        description: 'Scan tapdata'
        required: false
        type: string
        default: ''
      tapdata-enterprise:
        description: 'Scan tapdata-enterprise'
        required: false
        type: string
        default: ''
      tapdata-connectors:
        description: 'Scan tapdata-connectors'
        required: false
        type: string
        default: ''
      tapdata-connectors-enterprise:
        description: 'Scan tapdata-connectors-enterprise'
        required: false
        type: string
        default: ''
      tapdata-license:
        description: 'Scan tapdata-license'
        required: false
        type: string
        default: ''
      tapdata-cloud:
        description: 'Scan tapdata-cloud'
        required: false
        type: string
        default: ''
      tapdata-enterprise-web:
        description: 'Scan tapdata-enterprise-web'
        required: false
        type: string
        default: ''
      tapdata-application:
        description: 'Scan tapdata-application'
        required: false
        type: string
        default: ''
  workflow_call:
    inputs:
      tapdata:
        description: 'Scan tapdata'
        required: false
        type: string
        default: ''
      tapdata-enterprise:
        description: 'Scan tapdata-enterprise'
        required: false
        type: string
        default: ''
      tapdata-connectors:
        description: 'Scan tapdata-connectors'
        required: false
        type: string
        default: ''
      tapdata-connectors-enterprise:
        description: 'Scan tapdata-connectors-enterprise'
        required: false
        type: string
        default: ''
      tapdata-license:
        description: 'Scan tapdata-license'
        required: false
        type: string
        default: ''
      tapdata-cloud:
        description: 'Scan tapdata-cloud'
        required: false
        type: string
        default: ''
      tapdata-enterprise-web:
        description: 'Scan tapdata-enterprise-web'
        required: false
        type: string
        default: ''
      tapdata-application:
        description: 'Scan tapdata-application'
        required: false
        type: string
        default: 'main'
    secrets:
      TAPDATA_ENT_CICD_TOKEN:
        description: 'Tapdata enterprise cicd token'
        required: true
      SONAR_TOKEN:
        description: 'Sonar token'
        required: true
      SONAR_HOST:
        description: 'Sonar host'
        required: true
      SONAR_USER:
        description: 'Sonar user'
        required: true
      SONAR_PASS:
        description: 'Sonar pass'
        required: true
      INTERNAL_REPO:
        description: 'Internal repo'
        required: true

env:
  GITEE_USER: tapdata_1

jobs:

  Scan-Tapdata:
    runs-on: office-scan
    if: ${{ inputs.tapdata != '' }}
    timeout-minutes: 30
    steps:
      - name: Clean Directory
        run: |
          rm -rf ./*
      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh
          echo "Host gitee.com" > ~/.ssh/config
          echo "  StrictHostKeyChecking no" >> ~/.ssh/config
          chmod 600 ~/.ssh/config
      - name: Checkout Tapdata Code
        run: |
          git clone -b ${{ inputs.tapdata }} *************:${{ env.GITEE_USER }}/tapdata.git
          cd tapdata && git fetch --tags
      - name: Patch Maven Dependens
        run: |
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync --delete --password-file=/tmp/rsync.passwd -avz rsync://root@*************:873/data/enterprise-temp/tapdata/ /root/.m2/
      - name: Get OpenSource Branch Name
        run: |
          echo "OPENSOURCE_BRANCH=${{ inputs.tapdata }}" >> $GITHUB_ENV
      - name: Build Tapdata - And Analyze
        env:
          GITHUB_TOKEN: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        run: |
          update-alternatives --set java /usr/java/jdk-17.0.12/bin/java
          export JAVA_HOME=/usr/java/jdk-17.0.12
          cd tapdata && mvn clean test -T1C -Dmaven.compile.fork=true -P idaas && mvn sonar:sonar \
            -Dsonar.projectKey=daas -Dsonar.host.url=${{ secrets.SONAR_HOST }} \
            -Dsonar.login=${{ secrets.SONAR_USER }} -Dsonar.password=${{ secrets.SONAR_PASS }} \
            -Dsonar.branch.name=${{ env.OPENSOURCE_BRANCH }}
          update-alternatives --set java /usr/java/jdk1.8.0_202/bin/java
          export JAVA_HOME=/usr/java/jdk1.8.0_202
      - name: Prevent Skip Unittest
        run: |
          cd tapdata
          find ./ -name jacoco | grep iengine
          find ./ -name jacoco | grep manager
      - name: Install Dependens
        run: |
          apt install -y jq
      - name: SonarQube Quality Gate check
        uses: sonarsource/sonarqube-quality-gate-action@master
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        with:
          scanMetadataReportFile: tapdata/target/sonar/report-task.txt

  Scan-Tapdata-Connectors:
    runs-on: office-scan
    if: ${{ inputs.tapdata-connectors != '' }}
    timeout-minutes: 60
    steps:
      - name: Clean Directory
        run: |
          rm -rf ./*
      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh
          echo "Host gitee.com" > ~/.ssh/config
          echo "  StrictHostKeyChecking no" >> ~/.ssh/config
          chmod 600 ~/.ssh/config
      - name: Checkout Tapdata-Connectors Code
        run: |
          git clone -b ${{ inputs.tapdata-connectors }} *************:${{ env.GITEE_USER }}/tapdata-connectors.git
          cd tapdata-connectors && git fetch --tags
      - name: Patch Maven Dependens
        run: |
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync --delete --password-file=/tmp/rsync.passwd -avz rsync://root@*************:873/data/enterprise-temp/tapdata/ /root/.m2/
      - name: Get Tapdata-Connectors Branch Name
        run: |
          echo "CONNECTORS_BRANCH=${{ inputs.tapdata-connectors }}" >> $GITHUB_ENV
      - name: Build Tapdata-Connectors - And Analyze
        env:
          GITHUB_TOKEN: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        run: |
          update-alternatives --set java /usr/java/jdk-17.0.12/bin/java
          export JAVA_HOME=/usr/java/jdk-17.0.12
          cd tapdata-connectors && mvn clean test -T1C -Dmaven.compile.fork=true && mvn sonar:sonar \
            -Dsonar.projectKey=tapdata-connectors -Dsonar.host.url=${{ secrets.SONAR_HOST }} \
            -Dsonar.login=${{ secrets.SONAR_USER }} -Dsonar.password=${{ secrets.SONAR_PASS }} \
            -Dsonar.branch.name=${{ env.CONNECTORS_BRANCH }}
          update-alternatives --set java /usr/java/jdk1.8.0_202/bin/java
          export JAVA_HOME=/usr/java/jdk1.8.0_202
      - name: Install Dependens
        run: |
          apt install -y jq
      - name: SonarQube Quality Gate check
        uses: sonarsource/sonarqube-quality-gate-action@master
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        with:
          scanMetadataReportFile: tapdata-connectors/target/sonar/report-task.txt

  Scan-Tapdata-Connectors-Enterprise:
    runs-on: office-scan
    if: ${{ inputs.tapdata-connectors-enterprise != '' }}
    timeout-minutes: 60
    steps:
      - name: Clean Directory
        run: |
          rm -rf ./*
      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh
          echo "Host gitee.com" > ~/.ssh/config
          echo "  StrictHostKeyChecking no" >> ~/.ssh/config
          chmod 600 ~/.ssh/config
      - name: Checkout Tapdata-Connectors Code
        run: |
          git clone -<NAME_EMAIL>:${{ env.GITEE_USER }}/tapdata-connectors.git
          cd tapdata-connectors && git fetch --tags
      - name: Checkout Tapdata-Connectors-Enterprise Code
        run: |
          git clone -b ${{ inputs.tapdata-connectors-enterprise }} *************:${{ env.GITEE_USER }}/tapdata-connectors-enterprise.git
          cd tapdata-connectors-enterprise && git fetch --tags
      - name: Patch Maven Dependens
        run: |
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync --delete --password-file=/tmp/rsync.passwd -avz rsync://root@*************:873/data/enterprise-temp/tapdata/ /root/.m2/
      - name: Get Tapdata-Connectors-Enterprise Branch Name
        run: |
          echo "CONNECTORS_BRANCH=${{ inputs.tapdata-connectors-enterprise }}" >> $GITHUB_ENV
      - name: Build Tapdata-Connectors
        run: |
          update-alternatives --set java /usr/java/jdk-17.0.12/bin/java
          export JAVA_HOME=/usr/java/jdk-17.0.12
          cd tapdata-connectors && mvn install -T1C -Dmaven.compile.fork=true -DskipTests
      - name: Build Tapdata-Connectors-Enterprise - And Analyze
        env:
          GITHUB_TOKEN: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        run: |
          cd tapdata-connectors-enterprise && mvn clean test -T1C -Dmaven.compile.fork=true && mvn sonar:sonar \
            -Dsonar.projectKey=tapdata-connectors-enterprise -Dsonar.host.url=${{ secrets.SONAR_HOST }} \
            -Dsonar.login=${{ secrets.SONAR_USER }} -Dsonar.password=${{ secrets.SONAR_PASS }} \
            -Dsonar.branch.name=${{ env.CONNECTORS_BRANCH }}
          update-alternatives --set java /usr/java/jdk1.8.0_202/bin/java
          export JAVA_HOME=/usr/java/jdk1.8.0_202
      - name: Install Dependens
        run: |
          apt install -y jq
      - name: SonarQube Quality Gate check
        uses: sonarsource/sonarqube-quality-gate-action@master
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        with:
          scanMetadataReportFile: tapdata-connectors-enterprise/target/sonar/report-task.txt

  Scan-Tapdata-License:
    runs-on: office-scan
    if: ${{ inputs.tapdata-license != '' }}
    timeout-minutes: 30
    steps:
      - name: Clean Directory
        run: |
          rm -rf ./*
      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh
          echo "Host gitee.com" > ~/.ssh/config
          echo "  StrictHostKeyChecking no" >> ~/.ssh/config
          chmod 600 ~/.ssh/config
      - name: Checkout Tapdata-License Code
        run: |
          git clone -b ${{ inputs.tapdata-license }} *************:${{ env.GITEE_USER }}/tapdata-license.git
          cd tapdata-license && git fetch --tags
      - name: Checkout Tapdata Code
        run: |
          git clone -<NAME_EMAIL>:${{ env.GITEE_USER }}/tapdata.git
          cd tapdata && git fetch --tags
      - name: Patch Maven Dependens
        run: |
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync --delete --password-file=/tmp/rsync.passwd -avz rsync://root@*************:873/data/enterprise-temp/tapdata/ /root/.m2/
      - name: Get Tapdata-License Branch Name
        run: |
          echo "LICENSE_BRANCH=${{ inputs.tapdata-license }}" >> $GITHUB_ENV
      - name: Build Tapdata
        run: |
          update-alternatives --set java /usr/java/jdk-17.0.12/bin/java
          export JAVA_HOME=/usr/java/jdk-17.0.12
          cd tapdata && mvn clean install -T1C -Dmaven.compile.fork=true -P idaas
          update-alternatives --set java /usr/java/jdk1.8.0_202/bin/java
          export JAVA_HOME=/usr/java/jdk1.8.0_202
      - name: Build Tapdata-License - And Analyze
        env:
          GITHUB_TOKEN: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        run: |
          update-alternatives --set java /usr/java/jdk-17.0.12/bin/java
          export JAVA_HOME=/usr/java/jdk-17.0.12
          cd tapdata-license && mvn clean test -T1C -Dmaven.compile.fork=true && mvn sonar:sonar \
              -Dsonar.projectKey=tapdata-license -Dsonar.host.url=${{ secrets.SONAR_HOST }} \
              -Dsonar.login=${{ secrets.SONAR_USER }} -Dsonar.password=${{ secrets.SONAR_PASS }} \
              -Dsonar.branch.name=${{ env.LICENSE_BRANCH }}
          update-alternatives --set java /usr/java/jdk1.8.0_202/bin/java
          export JAVA_HOME=/usr/java/jdk1.8.0_202
      - name: Install Dependens
        run: |
          apt install -y jq
      - name: SonarQube Quality Gate check
        uses: sonarsource/sonarqube-quality-gate-action@master
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        with:
          scanMetadataReportFile: tapdata-license/target/sonar/report-task.txt

  Scan-Tapdata-Enterprise:
    runs-on: office-scan
    if: ${{ inputs.tapdata-enterprise != '' }}
    timeout-minutes: 30
    steps:
      - name: Clean Directory
        run: |
          rm -rf ./*
      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh
          echo "Host gitee.com" > ~/.ssh/config
          echo "  StrictHostKeyChecking no" >> ~/.ssh/config
          chmod 600 ~/.ssh/config
      - name: Checkout Tapdata Code
        run: |
          git clone -<NAME_EMAIL>:${{ env.GITEE_USER }}/tapdata.git
          cd tapdata && git fetch --tags
      - name: Checkout Tapdata-Enterprise Code
        run: |
          git clone -b ${{ inputs.tapdata-enterprise }} *************:${{ env.GITEE_USER }}/tapdata-enterprise.git
          cd tapdata-enterprise && git fetch --tags
      - name: Patch Maven Dependency
        run: |
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync --delete --password-file=/tmp/rsync.passwd -avz rsync://root@*************:873/data/enterprise-temp/tapdata/ /root/.m2/
      - name: Get Tapdata-Enterprise Branch Name
        run: |
          echo "ENTERPRISE_BRANCH=${{ inputs.tapdata-enterprise }}" >> $GITHUB_ENV
      - name: Build Tapdata
        run: |
          update-alternatives --set java /usr/java/jdk-17.0.12/bin/java
          export JAVA_HOME=/usr/java/jdk-17.0.12
          cd tapdata && mvn install -T1C -Dmaven.compile.fork=true -DskipTests -P daas -P enterprise
          update-alternatives --set java /usr/java/jdk1.8.0_202/bin/java
          export JAVA_HOME=/usr/java/jdk1.8.0_202
      - name: Build Tapdata-Enterprise (Java)
        env:
          GITHUB_TOKEN: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        run: |
          update-alternatives --set java /usr/java/jdk-17.0.12/bin/java
          export JAVA_HOME=/usr/java/jdk-17.0.12
          cd tapdata-enterprise && mvn clean test -T1C -Dmaven.compile.fork=true
          rm -rf apiserver tapdata-agent tickets jest.config.js
          mvn -B verify org.sonarsource.scanner.maven:sonar-maven-plugin:sonar -Dsonar.projectKey=tapdata-enterprise \
            -Dsonar.host.url=${{ secrets.SONAR_HOST }} -Dsonar.login=${{ secrets.SONAR_USER }} -Dsonar.password=${{ secrets.SONAR_PASS }} \
            -Dsonar.branch.name=${{ env.ENTERPRISE_BRANCH }}
          update-alternatives --set java /usr/java/jdk1.8.0_202/bin/java
          export JAVA_HOME=/usr/java/jdk1.8.0_202
#      - name: Scan Tapdata-Enterprise (NodeJS)
#        run: |
#          npm config set registry https://registry.npmmirror.com
#          cd tapdata-enterprise && npm install -g jest && npm install --save-dev ts-jest
#          npx jest --no-cache --coverage --passWithNoTests
#          /usr/local/sonar/bin/sonar-scanner -Dsonar.projectKey=tapdata-enterprise -Dsonar.host.url=${{ secrets.SONAR_HOST }} \
#            -Dsonar.login=${{ secrets.SONAR_USER }} -Dsonar.password=${{ secrets.SONAR_PASS }} \
#            -Dsonar.branch.name=${{ env.ENTERPRISE_BRANCH }} -Dsonar.javascript.lcov.reportPaths=coverage/lcov.info \
#            -Dsonar.java.binaries=**/classes/** -Dsonar.coverage.jacoco.xmlReportPaths=**/target/site/jacoco/jacoco.xml
      - name: Install Dependens
        run: |
          apt install -y jq
      - name: SonarQube Quality Gate check
        uses: sonarsource/sonarqube-quality-gate-action@master
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        with:
          scanMetadataReportFile: tapdata-enterprise/target/sonar/report-task.txt

  Scan-Tapdata-Enterprise-Web:
    runs-on: office-scan
    if: ${{ inputs.tapdata-enterprise-web != '' }}
    timeout-minutes: 30
    steps:
      - name: Clean Directory
        run: |
          rm -rf ./*
      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh
          echo "Host gitee.com" > ~/.ssh/config
          echo "  StrictHostKeyChecking no" >> ~/.ssh/config
          chmod 600 ~/.ssh/config
      - name: Checkout Tapdata-Enterprise-Web Code
        run: |
          git clone -b ${{ inputs.tapdata-enterprise-web }} *************:${{ env.GITEE_USER }}/tapdata-web.git tapdata-enterprise-web
          cd tapdata-enterprise-web && git fetch --tags
      - name: Get Tapdata-Enterprise-Web Branch Name
        run: |
          echo "ENTERPRISE_WEB_BRANCH=${{ inputs.tapdata-enterprise-web }}" >> $GITHUB_ENV
      - name: Scan Tapdata-Enterprise-Web
        run: |
          cd tapdata-enterprise-web && npm install -g jest && npm install --save-dev ts-jest --force
          npx jest --no-cache --coverage --passWithNoTests
          /usr/local/sonar/bin/sonar-scanner -Dsonar.projectKey=tapdata-enterprise-web -Dsonar.host.url=${{ secrets.SONAR_HOST }} \
            -Dsonar.login=${{ secrets.SONAR_USER }} -Dsonar.password=${{ secrets.SONAR_PASS }} \
            -Dsonar.branch.name=${{ env.ENTERPRISE_WEB_BRANCH }} -Dsonar.javascript.lcov.reportPaths=coverage/lcov.info
      - name: Install Dependens
        run: |
          apt install -y jq
      - name: SonarQube Quality Gate check
        uses: sonarsource/sonarqube-quality-gate-action@master
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        with:
          scanMetadataReportFile: tapdata-enterprise-web/.scannerwork/report-task.txt
