# [2025-04-29] 修改 GitHub Actions 配置以使用国内 DNS 服务器

**用户请求**
@.github/workflows/sync-code-to-office.yaml 这个配置现在看起来是外网访问gitee.com失败了，有没可能增加dns服务器，最好能添加国内的dns服务器

**会话目的**
- 解决 GitHub Actions Runner 无法访问 Gitee 的问题。

**完成的主要任务**
- 修改了 `.github/workflows/sync-code-to-office.yaml` 文件。
- 将所有 Job 中修改 `/etc/hosts` 的步骤替换为添加国内 DNS 服务器 (`***************`, `*********`) 到 `/etc/resolv.conf` 的步骤。

**关键决策和解决方案**
- 不再硬编码 Gitee 的 IP 地址到 `/etc/hosts`。
- 通过修改 `/etc/resolv.conf` 文件，优先使用国内 DNS 服务器进行域名解析，同时保留原始的 DNS 配置作为备用。

**使用的技术栈**
- GitHub Actions
- Bash Shell

**修改的文件**
- `.github/workflows/sync-code-to-office.yaml`
- `CHANGELOG.md`

# [2025-04-29] 创建 MongoDB 监控器用户指南

**用户请求**
@monitor/README.md 请根据此 README 文档，为 monitor 应用创建一个单独的用户使用说明文档。

**会话目的**
- 创建一个详细的用户指南，方便用户理解和使用 monitor 应用程序。

**完成的主要任务**
- 基于 `monitor/README.md` 的内容，创建了新的文档 `monitor/USAGE_GUIDE.md`。

**关键决策和解决方案**
- 从 `README.md` 中提取与应用程序使用相关的部分，包括简介、前提条件、运行方法、各项监控的配置选项和示例。
- 将提取的内容组织成结构清晰的用户指南格式。

**使用的技术栈**
- Markdown

**修改的文件**
- `monitor/USAGE_GUIDE.md` (创建)

# [2025-05-07] 添加任务调度均衡监控说明

**用户请求**
@README.md 当有多个引擎时，如果任务调度的不均衡，可以触发告警。

**会话目的**
- 为监控工具添加任务调度均衡监控的功能说明

**完成的主要任务**
- 在 README.md 中详细描述任务调度均衡监控的功能、API细节、告警规则、示例告警信息和所需配置项。
- 将 `taskbalance` 添加到 `README.md` 中支持的监控类型列表。

**关键决策和解决方案**
- 新增 "任务调度均衡监控 (`taskbalance`)" 小节到 `README.md` 的 "功能特性" 部分。
- 清晰地列出了新监控类型的目的、工作原理、API依赖、告警逻辑、告警消息格式以及如何通过命令行参数进行配置。
- 提供了API端点、参数和预期响应关键字段的示例。

**使用的技术栈**
- Markdown

**修改的文件**
- monitor/README.md
- CHANGELOG.md

# [2025-05-07] 改进任务调度均衡监控 (taskbalance)

**用户请求**
1. -taskbalance-api-url这个不应当要求输入整个url，URL是固定的，只需要输入ip和端口即可切换不同的环境
2. -taskbalance-process-ids不应当通过这个参数来指定引擎的id，应当通过以下api获取所有的引擎列表...

**会话目的**
- 优化 `taskbalance` 监控的配置方式和引擎发现机制。

**完成的主要任务**
- 修改 `monitor/config/config.go`：
    - 移除了 `-taskbalance-api-url` 和 `-taskbalance-process-ids` 命令行参数及其对应的配置字段。
    - 新增了 `-taskbalance-hostport` 命令行参数 (例如 `*************:3030`)，用于指定 API 服务器的主机和端口。
    - 更新了相关的帮助信息和验证逻辑。
- 修改 `monitor/taskbalance/taskbalance.go`：
    - 程序现在使用配置的 `hostport` 和固定的 API 路径 (`/api/clusterStates`, `/api/Workers/getProcessInfo`) 构造完整的请求 URL。
    - 实现了 `getActiveEngines` 函数，该函数调用 `/api/clusterStates` API (使用 `access_token` 和固定的 `index=1` 参数) 来动态获取所有 `engine.status` 为 "running" 的引擎的 `process_id` 和 `hostname`。
    - `CheckTaskBalance` 函数现在使用动态获取的引擎列表来调用 `/api/Workers/getProcessInfo`。
    - 告警日志中现在会包含引擎的 `hostname` 以提高可读性。
    - 定义了新的 Go 结构体以解析 `/api/clusterStates` 的 JSON 响应。
    - 添加了一个通用的 `makeAPIRequest` 辅助函数来简化 HTTP GET 请求的发送。

**关键决策和解决方案**
- 通过引入 `-taskbalance-hostport` 简化了 API 端点的配置。
- 通过调用 `/api/clusterStates` API 实现了引擎的自动发现，移除了手动配置引擎 ID 的需要，使得监控能够自动适应集群变化。
- 在日志中加入了引擎的 `hostname`，方便用户识别具体引擎。

**使用的技术栈**
- Golang
- JSON
- HTTP API

**修改的文件**
- monitor/config/config.go
- monitor/taskbalance/taskbalance.go
- CHANGELOG.md
- monitor/README.md

# [2025-05-23] 为 GitHub Actions 工作流增加 ApiServer 打包控制选项

**用户请求**
@.github/workflows/build-tapdata-op.yaml 请增加一个是否打包ApiServer的选项

**会话目的**
- 为 OP 版本完整编译工作流添加 ApiServer 组件的可选打包控制

**完成的主要任务**
- 在 GitHub Actions 工作流的输入参数中新增 `INCLUDE_APISERVER` 布尔选项，默认值为 `true`
- 在 `Set-Env` job 中添加对 `INCLUDE_APISERVER` 参数的处理和输出
- 在 `Build-Tapdata-Enterprise` job 中添加条件逻辑，根据 `INCLUDE_APISERVER` 选项控制：
  - ApiServer 依赖的同步 (rsync apiserver/node_modules)
  - 构建脚本参数 (传递 --exclude-apiserver 参数)
- 在 `build/build.sh` 脚本中添加 `--exclude-apiserver` 长参数支持：
  - 新增 `EXCLUDE_APISERVER` 变量，默认为 `false`
  - 修改 `build_tapdata_enterprise()` 函数，根据选项跳过 ApiServer 构建
  - 修改 `make_package_tapdata_enterprise()` 和 `make_package()` 函数，根据选项跳过 ApiServer 打包
  - 修改 `make_and_push_docker_image()` 和 `make_and_rsync_tar_file()` 函数，确保最终输出不包含 ApiServer
  - 更新 usage 函数显示新选项说明
- 在环境变量显示步骤中添加 `INCLUDE_APISERVER` 的输出
- **修复问题**：在所有相关的 GitHub Actions 步骤中传递 `--exclude-apiserver` 参数：
  - `Upload Tapdata Enterprise Component` 步骤 (`-k tapdata-enterprise`)
  - `Package and Push Image` 步骤 (`-o image`)
  - `Package and Push Tar File` 步骤 (`-o tar`)

**关键决策和解决方案**
- 使用长参数 `--exclude-apiserver` 而不是短参数，提高可读性
- 在构建、打包和最终输出的多个环节都添加条件控制，确保彻底排除 ApiServer
- 在依赖同步步骤添加条件判断，避免不必要的 ApiServer 依赖下载
- 保持向后兼容性，默认包含 ApiServer 以维持现有行为
- 在 docker 镜像和 tar 文件的最终生成阶段也确保移除 apiserver.tar.gz
- **修复长参数解析问题**：在 getopts 之前预处理长参数，避免 "illegal option" 错误
- **修复流水线问题**：确保在所有执行 build.sh 的步骤中都正确传递 `--exclude-apiserver` 参数

**使用的技术栈**
- GitHub Actions
- Bash Shell
- YAML

**修改的文件**
- .github/workflows/build-tapdata-op.yaml
- build/build.sh
- CHANGELOG.md 