apiVersion: v1
clusters:
- cluster:
    certificate-authority-data: 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
    server: https://************
  name: gke_crypto-reality-377106_asia-east2_tapdata-cloud-cluster
contexts:
- context:
    cluster: gke_crypto-reality-377106_asia-east2_tapdata-cloud-cluster
    user: gke_crypto-reality-377106_asia-east2_tapdata-cloud-cluster
  name: gke_crypto-reality-377106_asia-east2_tapdata-cloud-cluster
current-context: gke_crypto-reality-377106_asia-east2_tapdata-cloud-cluster
kind: Config
preferences: {}
users:
- name: gke_crypto-reality-377106_asia-east2_tapdata-cloud-cluster
  user:
    exec:
      apiVersion: client.authentication.k8s.io/v1beta1
      command: gke-gcloud-auth-plugin
      installHint: Install gke-gcloud-auth-plugin for use with kubectl by following
        https://cloud.google.com/blog/products/containers-kubernetes/kubectl-auth-changes-in-gke
      provideClusterInfo: true