import { setCurrentLanguage } from '@tap/i18n/src/shared/util'
import { DropdownList, MENU, SettingList } from '@/router/menu'
import routes from '@/router/routes'
import './styles/index.scss'

// S-重写路由
const parentRoute = routes.find((r) => r.path === '/')
const loginRoute = routes.find((r) => r.path === '/login')

parentRoute.children.push({
  path: 'about',
  name: 'about',
  component: () => import('./components/About.vue'),
  meta: {
    title: 'page_title_about',
  },
})
loginRoute.component = () => import('./components/Login')
// E-重写路由

export function install(router, i18n) {
  // 菜单
  // MENU.find(m => m.name === 'dataConsole').hidden = true
  // MENU.find(m => m.name === 'advancedFeatures').hidden = true
  MENU.find((m) => m.name === 'dataService').hidden = true
  // MENU.find(m => m.name === 'system').children.find(m => m.name === 'externalStorage').hidden = true
  MENU.find((m) => m.name === 'system').children.push(
    { name: 'License' },
    { name: 'settings', code: 'system_settings_menu' },
    { name: 'notificationSetting', code: 'home_notice_settings' },
  )

  // 顶部下拉菜单
  DropdownList.find((item) => item.name === 'version').route = '/about'
  DropdownList.find((item) => item.name === 'license').hidden = true

  // 系统设置
  // SettingList.find(item => item.key === 'webhookAlerts').hidden = true

  setCurrentLanguage('zh-CN', i18n)

  // 国际化
  i18n.merge({
    'zh-CN': {
      page_title_data_pipeline: '任务管理',
      page_title_about: '关于',
      app_account: '用户信息',
      account_accountSettings: '用户信息',
      app_version: '关于',
      page_title_license: '授权管理',
      page_title_data_hub: '数据中心',
      packages_ldp_data_hub_intro_how_do: '数据中心是如何工作的?',
      packages_ldp_data_hub_intro_scene_title: '哪些应用场景可以使用数据中心?',
      packages_ldp_data_hub_intro_title: '什么是数据中心?',
    },
    'zh-TW': {
      page_title_data_pipeline: '任務管理',
      page_title_about: '關於',
      app_account: '用戶信息',
      account_accountSettings: '用戶信息',
      app_version: '關於',
      page_title_license: '授權管理',
      page_title_data_hub: '數據中心',
      packages_ldp_data_hub_intro_how_do: '數據中心是如何工作的?',
      packages_ldp_data_hub_intro_scene_title: '哪些應用場景可以使用數據中心?',
      packages_ldp_data_hub_intro_title: '什麼是數據中心?',
    },
    en: {
      page_title_data_pipeline: 'Task Management',
      page_title_about: 'About',
      app_account: 'User Information',
      account_accountSettings: 'User Information',
      app_version: 'About',
      page_title_license: 'License Management',
      page_title_data_hub: 'Data Center',
      packages_ldp_data_hub_intro_how_do: 'How does the Data Center work?',
      packages_ldp_data_hub_intro_scene_title:
        'What scenarios can use Data Center?',
      packages_ldp_data_hub_intro_title: 'What is Data Center?',
    },
  })
}
