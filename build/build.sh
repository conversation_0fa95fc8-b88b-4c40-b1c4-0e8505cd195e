#!/bin/bash


java -version

# load log.sh
# script base dir
SCRIPT_BASE_DIR=$(dirname "$0")
. "$SCRIPT_BASE_DIR/log.sh"

# 2. show help
usage() {
echo "Usage: $0 [-c component_name] [-d] [-o output_type] [-t tag_name] [-m frontend_build_mode] [-s connectors] [--exclude-apiserver]" 1>&2
  echo
  echo "  Run this script under the root dir of this project. Build component and generate docker image or tar/zip file."
  echo
  echo "Options:"
  echo "  -c component_name: component name to be build, split with sep"
  echo "  -d clean build directory"
  echo "  -o output_type: output type (docker, tar)"
  echo "  -t tag_name: tag name (docker tag file name)"
  echo "  -m frontend_build_mode: frontend build mode (production, msa...)"
  echo "  -s connectors: connectors to be built (base64 string)"
  echo "  --exclude-apiserver: exclude apiserver from build and package"
  echo
  echo "Example:"
  echo "  $0 -c all -d true -o docker"
  echo "  $0 -c all -d true -o tar"
  echo "  $0 -c all -d true -o docker -m msa"
  echo "  $0 -c connectors-pdk -d true -o tar -m msa"
  echo "  $0 -c tapdata-enterprise --exclude-apiserver"
}

# 3. set default values
# available component list
AVAILABLE_COMPONENT_LIST=("frontend" "error-code-root" "plugin-kit" "file-storages" "open-manager" "connectors-common" "manager" "iengine" "connectors-javascript" "connectors" "tapdata-cli" "tapdata-agent")
# selected component list
SELECTED_COMPONENT_LIST=()
# clean build artifact directory
CLEAN_BUILD_DIR="false"
# tag name
TAG_NAME="latest"
# output type
OUTPUT_TYPE=""
# frontend build mode
FRONTEND_BUILD_MODE="production"
# connectors
B64_CONNECTORS=""
# project root dir
PROJECT_ROOT_DIR=$(cd "$SCRIPT_BASE_DIR/.." && pwd)
# output dir
OUTPUT_DIR="$PROJECT_ROOT_DIR/output"
# container build dir
CONTAINER_BUILD_DIR="$PROJECT_ROOT_DIR/docker_build"
# docker registry
DOCKER_REGISTRY="harbor.internal.tapdata.io"
# docker repository
DOCKER_REPOSITORY="$DOCKER_REGISTRY/tapdata/tapdata"
# package name
PACKAGE_NAME="tapdata-enterprise"
# enterprise directory
ENTERPRISE_DIR="$PROJECT_ROOT_DIR/../tapdata-enterprise"
# frontend directory
FRONTEND_DIR="$PROJECT_ROOT_DIR/../tapdata-web"
# opensource directory
OPENSOURCE_DIR="$PROJECT_ROOT_DIR/../tapdata"
# connector dir
CONNECTOR_DIR="$PROJECT_ROOT_DIR/../tapdata-connectors"
# enterprise connectors directory
ENTERPRISE_CONNECTORS_DIR="$PROJECT_ROOT_DIR/../tapdata-connectors-enterprise"
# common lib
COMMON_LIB_DIR="$PROJECT_ROOT_DIR/../tapdata-common-lib"
# license directory
LICENSE_DIR="$PROJECT_ROOT_DIR/../tapdata-license"
# git pull before build
GIT_PULL="false"
# Run Unittest
RUN_UNITTEST="true"
# make package
MAKE_PACKAGE=""
# start env to run auto test
AUTO_TEST_RUN=false
# option connectors to package
OPTION_CONNECTORS="all"
# build with SONAR
BUILD_WITH_SONAR="false"
# PARALLEL BUILD
PARALLEL_BUILD="false"
# build args
BUILD_ARGS=""
# Platform Version
PLATFORM_VERSION="Linux"
# exclude apiserver
EXCLUDE_APISERVER="false"
############ --start GKE Info start-- ############
# GKE Cluster Name
GKE_CLUSTER_NAME="tapdata-cloud-cluster"
GKE_CLUSTER_REGION="asia-east2"
GKE_PROJECT_NAME="crypto-reality-377106"
############ --end GKE Info end-- ############
############ --start SONAR HOST INFO-- ############
SONAR_HOST="http://*************:29000"
SONAR_USER=admin
SONAR_PASSWORD=Gotapd8!
############ --end SONAR HOST INFO-- ############
LOCAL_DEPLOY_USER="cicd"
LOCAL_DEPLOY_HOST="*************"

function get_last_stable_branch() {
  cd $OPENSOURCE_DIR
  STABLE_OPENSOURCE_BRANCH=$(git ls-remote --heads https://github.com/tapdata/tapdata/ --list 'release-v*' --sort=-version:refname | tail -n 1 | awk -F' ' '{print $2}' | awk -F'/' '{print $3}')
  cd $ENTERPRISE_DIR
  STABLE_ENTERPRISE_BRANCH=$(git ls-remote --heads https://github.com/tapdata/tapdata-enterprise/ --list 'release-v*' --sort=-version:refname | tail -n 1 | awk -F' ' '{print $2}' | awk -F'/' '{print $3}')
  cd $FRONTEND_DIR
  STABLE_FRONTEND_BRANCH=$(git ls-remote --heads https://github.com/tapdata/tapdata-enterprise-web/ --list 'release-v*' --sort=-version:refname | tail -n 1 | awk -F' ' '{print $2}' | awk -F'/' '{print $3}')

  echo "OPENSOURCE_BRANCH: $STABLE_OPENSOURCE_BRANCH"
  echo "ENTERPRISE_BRANCH: $STABLE_ENTERPRISE_BRANCH"
  echo "FRONTEND_BRANCH: $STABLE_FRONTEND_BRANCH"
}

# 4. get options
# First, process long options and remove them from arguments
ARGS=()
for arg in "$@"; do
  case $arg in
    --exclude-apiserver)
      EXCLUDE_APISERVER="true"
      ;;
    *)
      ARGS+=("$arg")
      ;;
  esac
done

# Reset positional parameters with only short options
set -- "${ARGS[@]}"

while getopts 'c:d:o:t:m:s:r:g:b:u:k:e:a:i:B:P:l:v:' OPT; do
	case "$OPT" in
	c)  # component name to be build or all (frontend, error-code-root, plugin-kit, file-storages, open-manager, connectors-common, iengine, connectors-javascript, connectors, tapdata-cli, tapdata-agent)
    COMPONENT_NAME="$OPTARG"
    ;;
  d)  # clean build directory
    CLEAN_BUILD_DIR="$OPTARG"
    ;;
  o)  # output type (docker, tar, zip)
    OUTPUT_TYPE="$OPTARG"
    ;;
  t)  # tag name (docker tag or zip/tar file name)
    TAG_NAME="$OPTARG"
    ;;
  m)  # frontend build mode (production, msa...)
    FRONTEND_BUILD_MODE="$OPTARG"
    ;;
  s)  # connectors to be built (base64 string) if -c is connectors
    B64_CONNECTORS="$OPTARG"
    ;;
  r)  # docker repository if -o is docker
    DOCKER_REPOSITORY="$OPTARG"
    ;;
  g)  # git pull before build
    GIT_PULL="$OPTARG"
    ;;
  u)  # if unittest run
    RUN_UNITTEST="$OPTARG"
    ;;
  k)  # make package
    MAKE_PACKAGE="$OPTARG"
    ;;
  e)  # echo last stable branch
    get_last_stable_branch
    exit 0
    ;;
  a)  # start env to run auto test
    AUTO_TEST_RUN="$OPTARG"
    ;;
  i) # option connectors to package
    OPTION_CONNECTORS="$OPTARG"
    ;;
  B) # build with SONAR
    BUILD_WITH_SONAR="$OPTARG"
    ;;
  P) # PARALLEL BUILD
    PARALLEL_BUILD="$OPTARG"
    ;;
  l) # build args
    BUILD_ARGS="$OPTARG"
    ;;
  v) # platform version
    PLATFORM_VERSION="$OPTARG"
    ;;
  ?)
    usage
    exit 1
    ;;
	esac
done


OPENSOURCE_BRANCH_NAME=$(cd $OPENSOURCE_DIR && git rev-parse --abbrev-ref HEAD)
ENTERPRISE_BRANCH_NAME=$(cd $ENTERPRISE_DIR && git rev-parse --abbrev-ref HEAD)
FRONTEND_BRANCH_NAME=$(cd $FRONTEND_DIR && git rev-parse --abbrev-ref HEAD)
if [[ "$PLATFORM_VERSION" != "Linux" && "$PLATFORM_VERSION" != "Darwin" && "$PLATFORM_VERSION" != "Windows" ]]; then
  error "Invalid platform version: $PLATFORM_VERSION"
fi

# check frontend build mode, if not in list [production, msa, ikas, chowsangsang], env REPLACE_FRONTEND_MODE will be true
if [[ "$FRONTEND_BUILD_MODE" != "production" && "$FRONTEND_BUILD_MODE" != "msa" && "$FRONTEND_BUILD_MODE" != "ikas" && "$FRONTEND_BUILD_MODE" != "chowsangsang" ]]; then
  REPLACE_FRONTEND_MODE="true"
else
  REPLACE_FRONTEND_MODE="false"
fi

info "The Env Setting List:"
cat <<EOF
  component name:                 $COMPONENT_NAME
  selected component list:        ${SELECTED_COMPONENT_LIST[@]}
  clean build artifact directory: $CLEAN_BUILD_DIR
  tag name:                       $TAG_NAME
  output type:                    $OUTPUT_TYPE
  frontend build mode:            $FRONTEND_BUILD_MODE
  connectors:                     $B64_CONNECTORS
  script base dir:                $SCRIPT_BASE_DIR
  project root dir:               $PROJECT_ROOT_DIR
  output dir:                     $OUTPUT_DIR
  container build dir:            $CONTAINER_BUILD_DIR
  docker repository:              $DOCKER_REPOSITORY
  package name:                   $PACKAGE_NAME
  frontend dir:                   $FRONTEND_DIR
  opensource dir:                 $OPENSOURCE_DIR
  connector dir:                  $CONNECTOR_DIR
  enterprise connectors dir:      $ENTERPRISE_CONNECTORS_DIR
  license dir:                    $LICENSE_DIR
  common lib dir:                 $COMMON_LIB_DIR
  git pull before build:          $GIT_PULL
  frontend branch name:           $FRONTEND_BRANCH_NAME
  opensource branch name:         $OPENSOURCE_BRANCH_NAME
  enterprise branch name:         $ENTERPRISE_BRANCH_NAME
  run unittest:                   $RUN_UNITTEST
  start env to run auto test:     $AUTO_TEST_RUN
  build with SONAR:               $BUILD_WITH_SONAR
  parallel build:                 $PARALLEL_BUILD
  option connectors to package:   $OPTION_CONNECTORS
  build args:                     $BUILD_ARGS
  platform version:               $PLATFORM_VERSION
  replace frontend mode:          $REPLACE_FRONTEND_MODE
  exclude apiserver:              $EXCLUDE_APISERVER
EOF

IFS=" " read -r -a COMPONENTS <<<"$(echo "$COMPONENT_NAME" | tr -d ' ' | tr ',' ' ')"

# 5. if -d is set, clean build directory
# (1) delete all component output directory
# (2) delete container build directory
# (3) delete mvn repository that is generated when building component
# (4) delete the docker image
#
# (1) delete all component output directory
function delete_component_output_dir() {
  # delete all component from the output directory
  rm -rf "$OUTPUT_DIR"
}
# (2) delete container build directory
function delete_container_build_dir() {
  rm -rf "$CONTAINER_BUILD_DIR"
}
# (3) delete mvn repository that is generated when building component
function delete_mvn_repository() {
  rm -rf "$HOME/.m2/repository/com/tapdata/"
  rm -rf "$HOME/.m2/repository/io/tapdata/"
}
# (4) delete the docker image if it exists
function delete_docker_image() {
  # find $DOCKER_REPOSITORY/tapdata:* images
  images=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep "$DOCKER_REPOSITORY/tapdata-enterprise")
  # delete all build images
  for image in $images; do
    docker rmi $image
  done
}

# if RUN_UNITTEST is not set, skip unittest
function build_java_component() {
  # set parallel run
  if [[ "$PARALLEL_BUILD" == "true" ]]; then
    parallel_build_arg="-T1C"
  fi
  # run unittest
  if [[ "$RUN_UNITTEST" == "false" ]]; then
    run_unittest="-DskipTests"
  fi
  # build with sonar or not
  if [[ "$BUILD_WITH_SONAR" == "true" ]]; then
    # mvn -B verify org.sonarsource.scanner.maven:sonar-maven-plugin:sonar -Dsonar.projectKey=tapdata -Dsonar.host.url=http://**************:9000 -Dsonar.login=admin -Dsonar.password=Gotapd8! $parallel_build_arg $run_unittest
    mvn clean test
    mvn -B verify org.sonarsource.scanner.maven:sonar-maven-plugin:sonar -Dsonar.projectKey=daas -Dsonar.host.url=$SONAR_HOST -Dsonar.login=$SONAR_USER -Dsonar.password=$SONAR_PASSWORD -Dsonar.branch.name=$OPENSOURCE_BRANCH_NAME $parallel_build_arg $run_unittest
  else
    mvn install $run_unittest $parallel_build_arg $BUILD_ARGS
  fi
  if [[ $? -ne 0 ]]; then
    error "Build Components Error."
  fi
}

function switch_node_version() {
  version=$1
  # install nvm to manage node version
  nvm > /dev/null
  if [[ $? -ne 0 ]]; then
    curl -o- https://gitee.com/RubyMetric/nvm-cn/raw/main/install.sh | bash
    chmod +x ~/.nvm/nvm.sh
    # nvm config
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh" # This loads nvm
    export NVM_NODEJS_ORG_MIRROR=https://npmmirror.com/mirrors/node
  fi
  nvm use $version
  if [[ $? -ne 0 ]]; then
    nvm install $version
    nvm use $version
  fi
}

function build_apiserver() {
  cd $ENTERPRISE_DIR/apiserver
  yarn config set registry https://registry.npmmirror.com
  yarn config set ignore-engines true
  npm install
}

# build enterprise: apiserver / tapdata-agent
function build_tapdata_enterprise() {
  if [[ "$EXCLUDE_APISERVER" == "false" ]]; then
    info "Starting to build tapdata apiserver."
    # switch node to version 18
    switch_node_version 16
    if [[ "$PLATFORM_VERSION" == "Windows" ]]; then
      # 解压 node_modules_win.tar.gz到 apiserver 目录下
      build_apiserver && tar -zxvf NDK/node_modules_win.tar.gz -C .
      rm -rf NDK/node_modules_win.tar.gz
    else
      build_apiserver
    fi
    switch_node_version 18
  else
    info "Skipping apiserver build (excluded)."
  fi
  info "Switch npm to ali_cloud registry."
  npm config set registry https://registry.npmmirror.com
  info "Starting to build tapdata tapdata-agent."
  cd $ENTERPRISE_DIR/tapdata-agent && bash build/build.sh
}

# add the license to tm.jar file
function add_license_to_tm() {
  bash $LICENSE_DIR/tapdata-license-client/builid/inject2Jar.sh $OPENSOURCE_DIR/manager/tm/target/tm-*-exec.jar
}

# build components
for COMPONENT in ${COMPONENTS[@]}; do
  if [[ $COMPONENT == "tapdata" ]]; then
    cd $OPENSOURCE_DIR && build_java_component
  elif [[ $COMPONENT == "tapdata-license" ]]; then
    cd $LICENSE_DIR && add_license_to_tm
  elif [[ $COMPONENT == "opensource-connectors" ]]; then
    if [[ ! -d $CONNECTOR_DIR/connectors/dist/ ]]; then
      mkdir -p $CONNECTOR_DIR/connectors/dist/
    fi
    cd $CONNECTOR_DIR && build_java_component
  elif [[ $COMPONENT == "enterprise-connectors" ]]; then
    if [[ ! -d $ENTERPRISE_CONNECTORS_DIR/connectors/dist/ ]]; then
      mkdir -p $ENTERPRISE_CONNECTORS_DIR/connectors/dist/
    fi
    cd $ENTERPRISE_CONNECTORS_DIR && build_java_component
  elif [[ $COMPONENT == "tapdata-enterprise" ]]; then
    cd $ENTERPRISE_DIR && build_tapdata_enterprise
  elif [[ $COMPONENT == "tapdata-enterprise-web" ]]; then
    cd $FRONTEND_DIR
    
    switch_node_version 20
    npm config set registry https://registry.npmmirror.com

    # Check if current tag version is >= v4.0.0
    FRONTEND_PATH="frontend"
    CURRENT_VERSION=$(git describe --tags --abbrev=0 2>/dev/null || echo "v0.0.0")

    info "- Tag: $CURRENT_VERSION"

    if [[ $(echo $CURRENT_VERSION | sed 's/v//g' | awk -F. '{ printf("%d%03d%03d\n", $1, $2, $3); }') -ge $(echo "4.0.0" | awk -F. '{ printf("%d%03d%03d\n", $1, $2, $3); }') ]]; then
      FRONTEND_PATH="frontend@4"
    else
      corepack prepare pnpm@8.5.0 --activate
      export NODE_OPTIONS=--openssl-legacy-provider
    fi

    cp $PROJECT_ROOT_DIR/build/${FRONTEND_PATH}/env/.env* $FRONTEND_DIR/apps/daas/
    cp $PROJECT_ROOT_DIR/build/${FRONTEND_PATH}/favicon/* $FRONTEND_DIR/apps/daas/public/static/favicon/
    cp -r $PROJECT_ROOT_DIR/build/${FRONTEND_PATH}/assets/* $FRONTEND_DIR/apps/daas/src/assets/

    if [ -d "$PROJECT_ROOT_DIR/build/${FRONTEND_PATH}/oem/$FRONTEND_BUILD_MODE" ]; then
      info "- MV oem/$FRONTEND_BUILD_MODE ..."
      mv -f "$PROJECT_ROOT_DIR/build/${FRONTEND_PATH}/oem/$FRONTEND_BUILD_MODE" $FRONTEND_DIR/apps/daas/src/oem/
    else
      info "- No oem/$FRONTEND_BUILD_MODE ..."
    fi

    DAAS_BUILD_NUMBER=$TAG_NAME
    
    bash build/build.sh -m $FRONTEND_BUILD_MODE || exit 1
  elif [[ $COMPONENT == "common-lib" ]]; then
    cd $COMMON_LIB_DIR && build_java_component
  else
    error "COMPONENT $COMPONENT provide error, type -h to get help"
    continue
  fi
done

# Tapdata Enterprise Package Dir like this:
#    --- tapdata  # tapdata-enterprise/tapdata-agent/dist/
#    --- application.yml  # if image version, this file is need, cp from tapdata-enterprise/tapdata-agent/dist/application.yml
#    --- etc/
#        --- logback.xml  # tapdata/manager/tm/target/classes/logback.xml
#        --- application-tm.yml  # tapdata/manager/tm/target/classes/application.yml
#        --- log4j2.yml  # tapdata-enterprise/tapdata-agent/dist/log4j2.yml
#        --- init/  # mkdir a new directory
#   --- connectors/
#        --- dist/ # tapdata-connectors/connectors/dist/ && tapdata-connectors-enterprise/connectors/dist/
#            if image version:
#            --- dist.tar.gz  # contain all connectors
#            if tar file:
#            --- mongodb-connector-v1.0-SNAPSHOT.jar
#            --- *-connector-v1.0-SNAPSHOT.jar
#            --- ...
#   --- lib/
#        --- NDK/  # tapdata-enterprise/build/NDK/
#            --- node/  # unzip node-v16.16.0-linux-x64.tar.xz and rename
#        --- pdk-deploy.jar  # tapdata/tapdata-cli/target/pdk.jar
#   --- components
#        --- tm.jar  # tapdata/manager/tm/target/tm-*-exec.jar
#        --- tapdata-agent.jar  # tapdata/iengine/ie.jar
#        --- apiserver.tar.gz  # tapdata-enterprise/apiserver/
#        --- webroot # frontend ui
#        --- ...

make_package_tapdata() {
  mkdir -p $OUTPUT_DIR/etc/init/ $OUTPUT_DIR/components/ $OUTPUT_DIR/lib/
  cd $OUTPUT_DIR/
  cp $OPENSOURCE_DIR/manager/tm/target/classes/logback.xml etc/logback.xml
  cp $OPENSOURCE_DIR/manager/tm/target/classes/application.yml etc/application-tm.yml
  mkdir -p components/
  cp $OPENSOURCE_DIR/manager/tm/target/tm-*-exec.jar components/tm.jar
  cp $OPENSOURCE_DIR/iengine/ie.jar components/tapdata-agent.jar
  cp $OPENSOURCE_DIR/tapdata-cli/target/pdk.jar lib/pdk-deploy.jar
}

make_package_connectors() {
  mkdir -p $OUTPUT_DIR/connectors/dist/
  cd $OUTPUT_DIR/
  if [[ $OPTION_CONNECTORS == "nightly-build" || $OPTION_CONNECTORS == "true" ]]; then
    ls $CONNECTOR_DIR/connectors/dist/ | grep -E "^(mongodb-connector|mysql-connector|oracle-connector)" | xargs -I {} cp $CONNECTOR_DIR/connectors/dist/{} connectors/dist/
    ls $ENTERPRISE_CONNECTORS_DIR/connectors/dist/ | grep -E "^(mongodb-connector|mysql-connector|oracle-connector)" | xargs -I {} cp $ENTERPRISE_CONNECTORS_DIR/connectors/dist/{} connectors/dist/
  elif [[ $OPTION_CONNECTORS == "lightweight" ]]; then
    # only dummy
    ls $CONNECTOR_DIR/connectors/dist/ | grep -E "^(dummy-connector)" | xargs -I {} cp $CONNECTOR_DIR/connectors/dist/{} connectors/dist/
    ls $ENTERPRISE_CONNECTORS_DIR/connectors/dist/ | grep -E "^(dummy-connector)" | xargs -I {} cp $ENTERPRISE_CONNECTORS_DIR/connectors/dist/{} connectors/dist/
  elif [[ $OPTION_CONNECTORS == "performance-test" ]]; then
    ls $CONNECTOR_DIR/connectors/dist/ | grep -E "^(clickhouse-connector|elasticsearch-connector|mongodb-connector|mssql-connector|mysql-connector|oracle-connector|postgres-connector|redis-connector|tidb-connector|kafka-connector|dummy-connector)" | xargs -I {} cp $CONNECTOR_DIR/connectors/dist/{} connectors/dist/
    ls $ENTERPRISE_CONNECTORS_DIR/connectors/dist/ | grep -E "^(clickhouse-connector|elasticsearch-connector|mongodb-connector|mssql-connector|mysql-connector|oracle-connector|postgres-connector|redis-connector|tidb-connector|kafka-connector|dummy-connector)" | xargs -I {} cp $ENTERPRISE_CONNECTORS_DIR/connectors/dist/{} connectors/dist/
  else
    cp -r $CONNECTOR_DIR/connectors/dist/* connectors/dist/
    cp -r $ENTERPRISE_CONNECTORS_DIR/connectors/dist/* connectors/dist/
  fi
}

make_package_tapdata_enterprise() {
  mkdir -p $OUTPUT_DIR/etc/ $OUTPUT_DIR/components/ $OUTPUT_DIR/lib/NDK/
  cd $OUTPUT_DIR/
  cp $ENTERPRISE_DIR/tapdata-agent/dist/application.yml ./
  cp $ENTERPRISE_DIR/tapdata-agent/dist/log4j2.yml etc/log4j2.yml
  mkdir -p lib/NDK/node/
  if [[ "$PLATFORM_VERSION" == "Linux" ]]; then
    if [[ "$FRONTEND_BUILD_MODE" == "oem" || "$FRONTEND_BUILD_MODE" == "datapp" ]]; then
      rsync -vzrt --password-file=/tmp/rsync.passwd rsync://root@*************:873/data/enterprise-artifact/tools/service_control ./
      chmod +x ./service_control
    else
      cp $ENTERPRISE_DIR/tapdata-agent/dist/tapdata ./
    fi
    tar -xf $ENTERPRISE_DIR/build/NDK/node-v16.16.0-linux-x64.tar.xz -C $ENTERPRISE_DIR/build/NDK/
    cp -r $ENTERPRISE_DIR/build/NDK/node-v16.16.0-linux-x64/* lib/NDK/node/
  elif [[ "$PLATFORM_VERSION" == "Windows" ]]; then
    if [[ "$FRONTEND_BUILD_MODE" == "oem" || "$FRONTEND_BUILD_MODE" == "datapp" ]]; then
      rsync -vzrt --password-file=/tmp/rsync.passwd rsync://root@*************:873/data/enterprise-artifact/tools/service_control.exe ./
      chmod +x ./service_control.exe
    else
      cp $ENTERPRISE_DIR/tapdata-agent/dist/tapdata.exe ./
    fi
    unzip $ENTERPRISE_DIR/build/NDK/node-v16.16.0-win-x64.zip -d $ENTERPRISE_DIR/build/NDK/
    mkdir -p lib/NDK/node/bin/
    cp -r $ENTERPRISE_DIR/build/NDK/node-v16.16.0-win-x64/* lib/NDK/node/bin/
  else
    error "PLATFORM_VERSION $PLATFORM_VERSION provide error, type -h to get help"
    exit 1
  fi
  if [[ "$EXCLUDE_APISERVER" == "false" ]]; then
    tar czf components/apiserver.tar.gz -C $ENTERPRISE_DIR/ apiserver/
  else
    info "Skipping apiserver packaging (excluded)."
  fi
}

make_package_common_lib() {
  mkdir -p $OUTPUT_DIR/lib/
  cd $OUTPUT_DIR/
  cp $COMMON_LIB_DIR/tapdata-cli/target/pdk.jar lib/pdk-deploy.jar
}

make_package_web() {
  mkdir -p $OUTPUT_DIR/components/webroot/
  cd $OUTPUT_DIR/
  cp -r $FRONTEND_DIR/dist/* components/webroot/
  if [[ $FRONTEND_BUILD_MODE == "ikas" ]]; then
    echo '{"oem":"ikas"}' > .config
  elif [[ $FRONTEND_BUILD_MODE == "datapp" ]]; then
    echo '{"oem":"datapp"}' > .config
  elif [[ $FRONTEND_BUILD_MODE == "normal" ]]; then
    echo '{"oem":"normal"}' > .config
  fi
}

function make_package() {
  # create output dir
  info "- Package Start..."
  if [[ ! -d $PROJECT_ROOT_DIR/output/ ]]; then
    mkdir $PROJECT_ROOT_DIR/output/
  fi
  cd $PROJECT_ROOT_DIR/output/
  if [[ "$FRONTEND_BUILD_MODE" == "oem" || "$FRONTEND_BUILD_MODE" == "datapp" ]]; then
    cp $ENTERPRISE_DIR/tapdata-agent/dist/tapdata ./service_control
    chmod +x ./service_control
  else
    cp $ENTERPRISE_DIR/tapdata-agent/dist/tapdata ./
    chmod +x ./tapdata
  fi
  cp $ENTERPRISE_DIR/tapdata-agent/dist/application.yml ./
  mkdir -p etc/init/
  cp $OPENSOURCE_DIR/manager/tm/target/classes/logback.xml etc/logback.xml
  cp $OPENSOURCE_DIR/manager/tm/target/classes/application.yml etc/application-tm.yml
  cp $ENTERPRISE_DIR/tapdata-agent/dist/log4j2.yml etc/log4j2.yml
  mkdir -p connectors/dist/
  cp -r $CONNECTOR_DIR/connectors/dist/* connectors/dist/
  cp -r $ENTERPRISE_CONNECTORS_DIR/connectors/dist/* connectors/dist/
  mkdir -p lib/NDK/node/
  tar -xf $ENTERPRISE_DIR/build/NDK/node-v16.16.0-linux-x64.tar.xz -C $ENTERPRISE_DIR/build/NDK/
  cp -r $ENTERPRISE_DIR/build/NDK/node-v16.16.0-linux-x64/* lib/NDK/node/
  cp $COMMON_LIB_DIR/tapdata-cli/target/pdk.jar lib/pdk-deploy.jar
  mkdir -p components/
  cp $OPENSOURCE_DIR/manager/tm/target/tm-*-exec.jar components/tm.jar
  
  if [[ "$FRONTEND_BUILD_MODE" == "oem" || "$FRONTEND_BUILD_MODE" == "datapp" ]]; then
    cp $OPENSOURCE_DIR/iengine/ie.jar components/app-agent.jar
  else
    cp $OPENSOURCE_DIR/iengine/ie.jar components/tapdata-agent.jar
  fi

  if [[ "$EXCLUDE_APISERVER" == "false" ]]; then
    tar czf components/apiserver.tar.gz -C $ENTERPRISE_DIR/ apiserver/
  else
    info "Skipping apiserver packaging (excluded)."
  fi
  if [[ $FRONTEND_BUILD_MODE == "ikas" ]]; then
    echo '{"oem":"ikas"}' > .config
  elif [[ $FRONTEND_BUILD_MODE == "datapp" ]]; then
    echo '{"oem":"datapp"}' > .config
  elif [[ $FRONTEND_BUILD_MODE == "normal" ]]; then
    echo '{"oem":"normal"}' > .config
  fi
  info "- Package Stop..."
}

function make_and_push_docker_image() {
  info "Start Make and Push Docker Image."

  echo "{\"oem\":\"$FRONTEND_BUILD_MODE\"}" > $OUTPUT_DIR/.config
  echo "{\"app_version\":\"$TAG_NAME\"}" > $OUTPUT_DIR/.version

  tar cfz $PROJECT_ROOT_DIR/output/connectors/dist.tar.gz -C $PROJECT_ROOT_DIR/output/connectors/ dist/
  cp $PROJECT_ROOT_DIR/build/image/Dockerfile $PROJECT_ROOT_DIR/output/
  cp $PROJECT_ROOT_DIR/build/image/docker-entrypoint.sh $PROJECT_ROOT_DIR/output/
  
  if [[ $REPLACE_FRONTEND_MODE == "true" ]]; then
    rsync -vzrt --password-file=/tmp/rsync.passwd rsync://root@*************:873/data/enterprise-artifact/tools/service_control $PROJECT_ROOT_DIR/output/
    chmod +x $PROJECT_ROOT_DIR/output/service_control
    sed -i 's/COPY tapdata \/tapdata\/apps\/tapdata/COPY service_control \/tapdata\/apps\/service_control/' $PROJECT_ROOT_DIR/output/Dockerfile

    mv $PROJECT_ROOT_DIR/output/components/tapdata-agent.jar $PROJECT_ROOT_DIR/output/components/app-agent.jar
    rm -rf $PROJECT_ROOT_DIR/output/components/webroot/docs
  fi
  
  if [[ "$EXCLUDE_APISERVER" == "true" ]]; then
    rm -rf $PROJECT_ROOT_DIR/output/components/apiserver.tar.gz
    info "Removed apiserver.tar.gz from docker image (excluded)."
  fi

  echo 'Gotapd8!' | docker login $DOCKER_REGISTRY --username=cicd --password-stdin
  docker build $PROJECT_ROOT_DIR/output/ -t $DOCKER_REPOSITORY/tapdata-enterprise:$TAG_NAME
  if [[ $? -eq 0 ]]; then
    info "Build Docker Image Success."
  else
    error "Build Docker Image Failed."
  fi
  # gcloud auth login --cred-file="$PROJECT_ROOT_DIR/build/keyfile.json" -q
  # gcloud auth configure-docker $DOCKER_REGISTRY -q
  # docker login -u flow-engine-1702457090174 -p 01b4d845fa04a6ee2ca72e669bac7b121e29ccf6 tapdata-docker.pkg.coding.net
  echo 'Gotapd8!' | docker login $DOCKER_REGISTRY --username=cicd --password-stdin
  docker push $DOCKER_REPOSITORY/tapdata-enterprise:$TAG_NAME
  if [[ $? -eq 0 ]]; then
    info "Push docker image successfully, tag: $TAG_NAME"
  else
    error "Push docker image failed, tag: $TAG_NAME"
  fi
  rm -rf $PROJECT_ROOT_DIR/output/connectors/dist.tar.gz
  rm -rf $PROJECT_ROOT_DIR/output/Dockerfile
}

function make_and_rsync_tar_file() {
  info "Make tar file Start, tag: $TAG_NAME"
  rm -rf $OUTPUT_DIR/application.yml
  rm -rf $OUTPUT_DIR/docker-entrypoint.sh
  
  echo "{\"oem\":\"$FRONTEND_BUILD_MODE\"}" > $OUTPUT_DIR/.config
  echo "{\"app_version\":\"$TAG_NAME\"}" > $OUTPUT_DIR/.version
  if [[ $REPLACE_FRONTEND_MODE == "true" ]]; then
    mkdir -p app && mv $OUTPUT_DIR/.config $OUTPUT_DIR/.version app/ && mv $OUTPUT_DIR/* app/ && mv app/ $OUTPUT_DIR/
    TAR_OUTPUT_DIR=$OUTPUT_DIR/app
  else
    mkdir -p tapdata && mv $OUTPUT_DIR/.config $OUTPUT_DIR/.version tapdata/ && mv $OUTPUT_DIR/* tapdata/ && mv tapdata/ $OUTPUT_DIR/
    TAR_OUTPUT_DIR=$OUTPUT_DIR/tapdata
  fi
  
  if [[ $REPLACE_FRONTEND_MODE == "true" ]]; then
    rm -f $TAR_OUTPUT_DIR/tapdata
    rsync -vzrt --password-file=/tmp/rsync.passwd rsync://root@*************:873/data/enterprise-artifact/tools/service_control $TAR_OUTPUT_DIR/
    chmod +x $TAR_OUTPUT_DIR/service_control

    mv $TAR_OUTPUT_DIR/components/tapdata-agent.jar $TAR_OUTPUT_DIR/components/app-agent.jar

    rm -rf $TAR_OUTPUT_DIR/components/webroot/docs
    rm -rf $TAR_OUTPUT_DIR/components/apiserver.tar.gz
  fi

  if [[ "$EXCLUDE_APISERVER" == "true" ]]; then
    rm -rf $TAR_OUTPUT_DIR/components/apiserver.tar.gz
    info "Removed apiserver.tar.gz from tar file (excluded)."
  fi

  # 同步 connectors 到 FileServer
  rsync --password-file=/tmp/rsync.passwd -vzrt --progress $TAR_OUTPUT_DIR/connectors/dist/* rsync://root@*************:873/data/enterprise-artifact/connectors/$FRONTEND_BUILD_MODE-tapdata-enterprise-$TAG_NAME/
  rm -rf $TAR_OUTPUT_DIR/connectors/dist/

  # async profile
  wget http://*************:5244/d/tools/async-profiler-3.0-linux-x64.tar.gz?sign=lwX2LlQGKO0Ph3M9AuygTGki9R_d76zTeRC5x_Gzzs8=:0 -O $TAR_OUTPUT_DIR/async-profiler.tar.gz
  tar -xzf $TAR_OUTPUT_DIR/async-profiler.tar.gz -C $TAR_OUTPUT_DIR/
  rm -f $TAR_OUTPUT_DIR/async-profiler.tar.gz $TAR_OUTPUT_DIR/async-profiler.tar.gz
  mv $TAR_OUTPUT_DIR/async-profiler-* $TAR_OUTPUT_DIR/async-profiler

  tar cfz $PROJECT_ROOT_DIR/$FRONTEND_BUILD_MODE-tapdata-enterprise-$TAG_NAME.tar.gz -C $PROJECT_ROOT_DIR/output/ .
  info "Start Rsync tar file to FileServer."
  echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
  rsync --password-file=/tmp/rsync.passwd -vzrt --progress $PROJECT_ROOT_DIR/$FRONTEND_BUILD_MODE-tapdata-enterprise-$TAG_NAME.tar.gz rsync://root@*************:873/data/enterprise-artifact/gz/
  info "Rsync tar file successfully, tag: $TAG_NAME"
}

if [[ $MAKE_PACKAGE == "all" ]]; then
  make_package
elif [[ $MAKE_PACKAGE == "tapdata" ]]; then
  make_package_tapdata
elif [[ $MAKE_PACKAGE == "connectors" ]]; then
  make_package_connectors
elif [[ $MAKE_PACKAGE == "tapdata-enterprise" ]]; then
  make_package_tapdata_enterprise
elif [[ $MAKE_PACKAGE == "common_lib" ]]; then
  make_package_common_lib
elif [[ $MAKE_PACKAGE == "web" || $MAKE_PACKAGE == "tapdata-enterprise-web" ]]; then
  make_package_web
elif [[ $MAKE_PACKAGE != "" ]]; then
  warn "-k not match any components"
fi

if [[ $OUTPUT_TYPE == "docker" || $OUTPUT_TYPE == "image" ]]; then
  make_and_push_docker_image
elif [[ $OUTPUT_TYPE == "tar" ]]; then
  make_and_rsync_tar_file
fi

if [ "$CLEAN_BUILD_DIR" = "true" ]; then
  info "Clean build and output directory..."
  # delete all component output directory
  delete_component_output_dir
  # delete container build directory
  delete_container_build_dir
  # delete mvn repository that is generated when building component
  delete_mvn_repository
  # delete the docker image if it exists
  delete_docker_image
  info "Clean Done."
fi

allow_port() {
  # 添加 Port 到 firewall 并 重载配置
  PORT=$1

  if [[ -z $PORT ]]; then
    echo "缺少端口 如 add_port 80"
    exit 1
  fi

  echo "~ 开放端口中..."
  firewall-cmd --zone=public --add-port=$PORT/tcp --permanent
  if [[ $? -ne 0 ]]; then
    echo "x 开放端口失败"
    exit 1
  else
    echo "~ 开放端口成功..."
  fi

  echo "~ 重载防火墙配置中..."
  firewall-cmd --reload
  if [[ $? -ne 0 ]]; then
    echo "x 重载防火墙失败..."
  else
    echo "~ 重载防火墙配置成功..."
  fi
}

deploy_to_gke() {
  info "Start Deploy to GKE..."
  info "Render yaml files..."
  deployment_name=$(echo $TAG_NAME | tr 'A-F' 'a-f' | sed 's/\.//g')
  sed -i "s/--version--/$deployment_name/g" $PROJECT_ROOT_DIR/build/service/service.yaml
  sed -i "s/--image-tag--/$TAG_NAME/g" $PROJECT_ROOT_DIR/build/service/service.yaml
  info "Start Login GKE..."
  gcloud auth login --cred-file="$PROJECT_ROOT_DIR/build/keyfile.json" -q
  gcloud config set project $GKE_PROJECT_NAME
  gcloud container clusters get-credentials $GKE_CLUSTER_NAME --region=$GKE_CLUSTER_REGION
  info "Start Deploy..."
  kubectl apply -f $PROJECT_ROOT_DIR/build/service/service.yaml
  while true; do
    info "Waiting for deployment to be Created..."
    kubectl get deployment -n dev | grep $deployment_name
    if [[ $? -eq 0 ]]; then
      info "Deployment Created."
      break
    fi
    sleep 5
  done
  info "Start watch deployment status..."
  for i in {1..20}; do
    sleep 30
    kubectl get pods -n dev | grep tapdata-$deployment_name | grep Running
    if [[ $? -ne 0 ]]; then
      warn "Deployment not Running."
      continue
    fi
    kubectl logs -f -n dev $(kubectl get pods -n dev | grep tapdata-$deployment_name | grep Running | awk '{print $1}') &
    break
  done
  kubectl rollout status deployment tapdata-$deployment_name -n dev --timeout=900s
  if [[ $? -eq 0 ]]; then
    info "Deployment Success."
  else
    error "Deployment Failed."
  fi
  info "Start get service external ip:port"
  kubectl get service tapdata-$deployment_name -n dev -o jsonpath='{.status.loadBalancer.ingress[0].ip}:{.spec.ports[?(@.name=="web")].port}' > .service_ip_port
  if [[ $? -eq 0 ]]; then
    info "Get service external ip:port Success."
  else
    error "Get service external ip:port Failed."
  fi
}

deploy_to_local_with_docker_compose() {
  chmod 600 $PROJECT_ROOT_DIR/build/id_rsa
  ssh_cmd="ssh -i $PROJECT_ROOT_DIR/build/id_rsa -o StrictHostKeyChecking=no $LOCAL_DEPLOY_USER@$LOCAL_DEPLOY_HOST"
  info "Start Deploy to Local..."
  version_name=$(echo $TAG_NAME | tr 'A-F' 'a-f' | sed 's/\.//g')
  info "Get Port not used..."

  # 获取未使用的端口，范围在30000-31000之间，增加随机性和重试机制
  get_unused_port() {
    for i in {1..5}; do  # 重试5次
      port=$($ssh_cmd 'bash -c "comm -23 <(shuf -i 30000-31000 -n 100 | sort) <(ss -tan | awk '\''{print \$4}'\'' | grep -Eo '\''[0-9]+\$'\'' | sort -u) | awk '\''NR==1'\''"')
      if ! $ssh_cmd "nc -zv 127.0.0.1 $port"; then  # 验证端口是否未被占用
        echo $port
        return
      else
        warn "Port $port is in use, retrying..."
      fi
    done
    error "Failed to find an unused port after 5 attempts."
    exit 1
  }

  port3030=$(get_unused_port)

  # 确保 port3080 与 port3030 不同
  while : ; do
    port3080=$(get_unused_port)
    if [[ "$port3080" -ne "$port3030" ]]; then
      break
    fi
    warn "Port 3080 ($port3080) is the same as Port 3030 ($port3030), retrying..."
  done

  info "Render docker-compose.yml..."
  echo "Port 3030: $port3030"
  echo "Port 3080: $port3080"
  echo "Version Name: $version_name"
  sed -i "s/--port3030--/$port3030/g" $PROJECT_ROOT_DIR/build/docker-compose/docker-compose.yaml
  sed -i "s/--port3080--/$port3080/g" $PROJECT_ROOT_DIR/build/docker-compose/docker-compose.yaml
  sed -i "s/--version--/$version_name/g" $PROJECT_ROOT_DIR/build/docker-compose/docker-compose.yaml
  sed -i "s/--image-tag--/$TAG_NAME/g" $PROJECT_ROOT_DIR/build/docker-compose/docker-compose.yaml
  sed -i "s/--registry--/$DOCKER_REGISTRY/g" $PROJECT_ROOT_DIR/build/docker-compose/docker-entrypoint.sh
  if [[ $? -ne 0 ]]; then
    error "Render docker-compose.yml Failed."
  fi

  info "Send docker-compose.yml to remote host..."
  $ssh_cmd "test -d /app/tapdata-$version_name"
  if [[ $? -ne 0 ]]; then
    info "Create directory /app/tapdata-$version_name"
    $ssh_cmd "mkdir -p /app/tapdata-$version_name"
  else
    info "Directory /app/tapdata-$version_name already exists"
  fi
  scp -i $PROJECT_ROOT_DIR/build/id_rsa  \
      -o StrictHostKeyChecking=no \
      $PROJECT_ROOT_DIR/build/docker-compose/* $LOCAL_DEPLOY_USER@$LOCAL_DEPLOY_HOST:/app/tapdata-$version_name/
  $ssh_cmd "chmod +x /app/tapdata-$version_name/docker-entrypoint.sh"

  # 增加重试机制来启动 docker compose
  for i in {1..3}; do
    info "Attempt $i: Starting Docker Compose..."
    $ssh_cmd "cd /app/tapdata-$version_name && sudo docker compose up -d"
    if [[ $? -eq 0 ]]; then
      info "Docker Compose started successfully."
      break
    else
      warn "Attempt $i failed. Retrying in 10 seconds..."
      sleep 10
    fi
  done

  info "watch deploy status..."
  $ssh_cmd "sudo docker logs -f tapdata-$version_name &" &
  for i in {1..20}; do
    sleep 30
    $ssh_cmd "sudo docker ps | grep tapdata-$version_name | grep '(healthy)'"
    if [[ $? -ne 0 ]]; then
      warn "tapdata not Running."
      continue
    fi
    break
  done
  $ssh_cmd "sudo docker ps | grep tapdata-$version_name | grep '(healthy)'"
  if [[ $? -eq 0 ]]; then
    info "tapdata Success."
  else
    error "tapdata Failed."
  fi

  echo "$LOCAL_DEPLOY_HOST:$port3030" > .service_ip_port
  allow_port $port3030
  allow_port $port3080
}

if [[ $AUTO_TEST_RUN == "true" ]]; then
#   deploy_to_gke
  deploy_to_local_with_docker_compose
fi
