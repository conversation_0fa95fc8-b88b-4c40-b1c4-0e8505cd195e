#!/bin/bash

# script base dir
SCRIPT_BASE_DIR=$(dirname "$0")
BASE_DIR=$(cd "$SCRIPT_BASE_DIR/../.." && pwd)
# load log.sh
. "$SCRIPT_BASE_DIR/log.sh"

############ -Start Set Variables- ############
ACTION=""                                                     # action to perform, one of: set-tag
TAPDATA_CLOUD_DIR="$BASE_DIR/tapdata-cloud"                   # tapdata-cloud dir
TAPDATA_DIR="$BASE_DIR/tapdata"                               # tapdata dir
TAPDATA_ENTERPRISE_DIR="$BASE_DIR/tapdata-enterprise"         # tapdata-enterprise dir
TAPDATA_WEB_DIR="$BASE_DIR/tapdata-web"                       # tapdata-web dir
TAG=""                                                        # artifact tag
COMPONENT=""                                                  # component to build, one of: console, tcm, tm-java, agent, tapdata_agent, ticket
ENV=""                                                        # environment, one of: gcp-dev, gcp-test, gcp-gray, gcp-net, gcp-prod
############ -End Set Variables- ############

help() {
  echo "Usage: $0 --action=<action>"
  echo "  --action=<action>  action to perform, one of: set-tag, update"
  echo "    <action>         set-tag: set artifact tag"
  echo "                     update : build and update component"
  echo "  --tag=<tag>        artifact tag"
  echo "  --component=<component>"
  echo "                     component to build, one of: console, tcm, tm-java, agent, tapdata_agent, ticket"
  echo "  --env=<env>        environment, one of: gcp-dev, gcp-test, gcp-gray, gcp-net, gcp-prod"
  exit 1
}

for i in "$@"; do
  case $i in
  --action=*)
    ACTION="${i#*=}"
    shift
    ;;
  --tag=*)
    TAG="${i#*=}"
    shift
    ;;
  --component=*)
    COMPONENT="${i#*=}"
    shift
    ;;
  --env=*)
    ENV="${i#*=}"
    shift
    ;;
  *)
    help
    ;;
  esac
done

### Show Variables ###
### -Start- ###
# cat <<EOF
# --------------------- Setting -----------------------------
# ACTION                        : $ACTION
# TAG                           : $TAG
# COMPONENT                     : $COMPONENT
# ENV                           : $ENV
# --------------------- Directory ---------------------------
# TAPDATA_DIR                   : $TAPDATA_DIR
# TAPDATA_CLOUD_DIR             : $TAPDATA_CLOUD_DIR
# TAPDATA_ENTERPRISE_DIR        : $TAPDATA_ENTERPRISE_DIR
# TAPDATA_ENTERPRISE_WEB_DIR    : $TAPDATA_ENTERPRISE_WEB_DIR
# EOF

# set artifact tag
# format: $(git short_tag)-$(hex time of current)
set_tag() {
  cd $TAPDATA_CLOUD_DIR
  short_tag=$(git branch --show-current | cut -d '-' -f 2)
  hex_time=$(date +%s | xargs printf '%x')
  tag="$short_tag-$hex_time"
  echo "$tag"
}

# build and update frontend
build_and_update_frontend() {
  cd $TAPDATA_WEB_DIR && rm -rf node_modules
  pnpm i
  if [[ "$ENV" == "gcp-dev" || "$ENV" == "gcp-test" ]]; then
    cd $TAPDATA_CLOUD_DIR && bash ./drs/build/build.sh -P dfs -v $TAG -p $ENV -c console -w docker-compose
  elif [[ "$ENV" == "gcp-gray" || "$ENV" == "gcp-net" || "$ENV" == "gcp-prod" ]]; then
    cd $TAPDATA_CLOUD_DIR && bash ./drs/build/build.sh -P dfs -v $TAG -p $ENV -c console
  else
    error "unknown env: $ENV"
  fi
}

build_and_update_tapflow() {
  cd $TAPDATA_CLOUD_DIR && bash ./drs/build/build.sh -P dfs -v $TAG -p $ENV -c tapflow
}

# build and update tcm
build_and_update_tcm() {
  if [[ "$ENV" == "gcp-dev" || "$ENV" == "gcp-test" ]]; then
    cd $TAPDATA_CLOUD_DIR && ./drs/build/build.sh -P dfs -v $TAG -p $ENV -c tcm -w docker-compose
  elif [[ "$ENV" == "gcp-gray" || "$ENV" == "gcp-net" || "$ENV" == "gcp-prod" ]]; then
    cd $TAPDATA_CLOUD_DIR && ./drs/build/build.sh -P dfs -v $TAG -p $ENV -c tcm
  else
    error "unknown env: $ENV"
  fi
}

# build and update tm-java
build_and_update_tm_java() {
  if [[ -f "$TAPDATA_CLOUD_DIR/drs/build/build.py" ]]; then
    if [[ "$ENV" == "gcp-dev" || "$ENV" == "gcp-test" ]]; then
      cd $TAPDATA_CLOUD_DIR && python3 ./drs/build/build.py -c manager -p "$TAPDATA_DIR/pom.xml" \
        -n http://maven.apache.org/POM/4.0.0 -m true \
        -s "./drs/build/build.sh -P dfs -v $TAG -p $ENV -c tm-java -w docker-compose"
    elif [[ "$ENV" == "gcp-gray" || "$ENV" == "gcp-net" || "$ENV" == "gcp-prod" ]]; then
      cd $TAPDATA_CLOUD_DIR && python3 ./drs/build/build.py -c manager -p "$TAPDATA_DIR/pom.xml" \
        -n http://maven.apache.org/POM/4.0.0 -m true \
        -s "./drs/build/build.sh -P dfs -v $TAG -p $ENV -c tm-java"
    else
      error "unknown env: $ENV"
    fi
  else
    if [[ "$ENV" == "gcp-dev" || "$ENV" == "gcp-test" ]]; then
      cd $TAPDATA_CLOUD_DIR && ./drs/build/build.sh -P dfs -v $TAG -p $ENV -c tm-java -w docker-compose
    elif [[ "$ENV" == "gcp-gray" || "$ENV" == "gcp-net" || "$ENV" == "gcp-prod" ]]; then
      cd $TAPDATA_CLOUD_DIR && ./drs/build/build.sh -P dfs -v $TAG -p $ENV -c tm-java
    else
      error "unknown env: $ENV"
    fi
  fi
}

# build and update ticket
build_and_update_ticket() {
  if [[ "$ENV" == "gcp-dev" || "$ENV" == "gcp-test" ]]; then
    cd $TAPDATA_CLOUD_DIR && ./drs/build/build.sh -P dfs -v $TAG -p $ENV -c ticket -w docker-compose
  elif [[ "$ENV" == "gcp-gray" || "$ENV" == "gcp-net" || "$ENV" == "gcp-prod" ]]; then
    cd $TAPDATA_CLOUD_DIR && ./drs/build/build.sh -P dfs -v $TAG -p $ENV -c ticket
  else
    error "unknown env: $ENV"
  fi
}

# build and update agent
build_and_update_agent() {
  if [[ -f "$TAPDATA_CLOUD_DIR/drs/build/build.py" ]]; then
    cd $TAPDATA_CLOUD_DIR &&
      python3 drs/build/build.py -c iengine -p '../tapdata/pom.xml' \
        -n http://maven.apache.org/POM/4.0.0 -m true \
        -s "./drs/build/build.sh -P dfs -v $TAG -p $ENV -c agent,tapdata_agent -u false"
  else
    cd $TAPDATA_CLOUD_DIR && ./drs/build/build.sh -P dfs -v $TAG -p $ENV -c agent,tapdata_agent -u false
  fi
}

# upload tapdata_agent to cdn
upload_to_cdn() {
  # build tapdata_agent
  if [[ "$ENV" == "gcp-dev" || "$ENV" == "gcp-test" ]]; then
    flag=test
  elif [[ "$ENV" == "gcp-gray" || "$ENV" == "gcp-net" || "$ENV" == "gcp-prod" ]]; then
    flag=feagent
  else
    error "unknown env: $ENV"
  fi
  # mkdir output dir
  OUTPUT_PATH="$TAPDATA_CLOUD_DIR/../dfs-$TAG"
  mkdir -p $OUTPUT_PATH && chmod -R 0755 $OUTPUT_PATH
  chmod 0600 $TAPDATA_CLOUD_DIR/drs/build/profiles/dev/public.pem
  # file Check
  if [[ ! -f "$TAPDATA_CLOUD_DIR/drs/dist/dfs-$TAG/bin/tapdata-agent-win-x64.exe" ]]; then
    error "tapdata-agent-win-x64.exe not found"
  fi
  if [[ ! -f "$TAPDATA_CLOUD_DIR/drs/dist/dfs-$TAG/bin/tapdata-agent-linux-arm64" ]]; then
    error "tapdata-agent-linux not found"
  fi
  if [[ ! -f "$TAPDATA_CLOUD_DIR/drs/dist/dfs-$TAG/bin/tapdata-agent-linux-x64" ]]; then
    error "tapdata-agent-linux-x64 not found"
  fi
  if [[ ! -f "$TAPDATA_CLOUD_DIR/drs/dist/dfs-$TAG/lib/connector-manager.jar" ]]; then
    error "connector-manager.jar not found"
  fi
  if [[ ! -f "$TAPDATA_CLOUD_DIR/drs/dist/dfs-$TAG/conf/log4j2.yml" ]]; then
    error "log4j2.yml not found"
  fi
  echo 'Gotapd8!' >/tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
  rsync -vzrt --password-file=/tmp/rsync.passwd rsync://root@58.251.34.123:873/data/enterprise-artifact/tools/tapdata $OUTPUT_PATH/tapdata
  rsync -vzrt --password-file=/tmp/rsync.passwd rsync://root@58.251.34.123:873/data/enterprise-artifact/tools/tapdata.exe $OUTPUT_PATH/tapdata.exe
  # update to cdn
  cp $TAPDATA_CLOUD_DIR/drs/dist/dfs-$TAG/lib/connector-manager.jar $OUTPUT_PATH/tapdata-agent
  cp $TAPDATA_CLOUD_DIR/drs/dist/dfs-$TAG/conf/log4j2.yml $OUTPUT_PATH/log4j2.yml
  echo "U2FsdGVkX1/MEMW+D0y6GRykxjVgkUfjDMi7tDFXan5e4eU4Iu4dDA/iKZZWFLWgrj3u0yTBYEMCrRaz8h58SNGOwHIAypaoU03KpZhwMsKomtzUPqw6msOL3U7dXkAqjryAOqY2QSv5M3A=" >"$OUTPUT_PATH/.version"
  chmod +x $OUTPUT_PATH/tapdata.exe
  chmod +x $OUTPUT_PATH/tapdata
  cd $OUTPUT_PATH/
  md5sum .version >.md5sum
  md5sum tapdata.exe >>.md5sum
  md5sum tapdata >>.md5sum
  md5sum tapdata-agent >>.md5sum
  md5sum log4j2.yml >>.md5sum
  cd ..
  wget 'https://gosspublic.alicdn.com/ossutil/1.7.15/ossutil-v1.7.15-linux-amd64.zip?spm=a2c4g.11186623.0.0.7d3f4edc1WL2Jt&file=ossutil-v1.7.15-linux-amd64.zip' -O /tmp/ossutils.zip
  unzip /tmp/ossutils.zip -d /tmp/ossutils
  path="/tmp/ossutils"
  cd $path/ossutil-* && mv ./* ../ && cd $current_path
  chmod -R u+x /tmp/ossutils
  /tmp/ossutils/ossutil -c $TAPDATA_CLOUD_DIR/drs/build/.ossutilconfig cp -r $OUTPUT_PATH/ "oss://tapdata-cdn-beijing/package/$flag/dfs-$TAG/"
}

main() {
  if [[ "$ACTION" == "" ]]; then
    error "action is required"
  elif [[ "$ACTION" == "set-tag" ]]; then
    if [[ "$TAPDATA_CLOUD_DIR" == "" ]]; then
      error "tapdata-cloud-dir is required"
    fi
    set_tag
  elif [[ "$ACTION" == "update" ]]; then
    # param check
    if [[ "$TAG" == "" ]]; then
      error "tag is required"
    fi
    if [[ "$ENV" == "" ]]; then
      error "ENV is required"
    fi
    if [[ "$COMPONENT" == "" ]]; then
      error "component is required"
    fi
    # component to build
    if [[ "$COMPONENT" == "console" ]]; then
      build_and_update_frontend
    elif [[ "$COMPONENT" == "tapflow" ]]; then
      build_and_update_tapflow
    elif [[ "$COMPONENT" == "tcm" ]]; then
      build_and_update_tcm
    elif [[ "$COMPONENT" == "tm-java" ]]; then
      build_and_update_tm_java
    elif [[ "$COMPONENT" == "ticket" ]]; then
      build_and_update_ticket
    elif [[ "$COMPONENT" == "agent,tapdata_agent" ]]; then
      build_and_update_agent
      upload_to_cdn
    fi
  else
    error "unknown action: $ACTION"
  fi
}

main
