version: "3"
services:
  tapdata---version--:
    container_name: tapdata---version--
    image: harbor.internal.tapdata.io/tapdata/tapdata/tapdata-enterprise:--image-tag--
    networks:
      - tapdata
    ports:
      - "--port3030--:3030"
      - "--port3080--:3080"
    deploy:
      resources:
        limits:
          cpus: "4"
          memory: 8G
        reservations:
          cpus: "4"
          memory: 8G
    environment:
      - MONGODB_CONNECTION_STRING=*************:37017/tapdata---version--?authSource=admin
      - MONGODB_PASSWORD=Gotapd8!
      - MONGODB_USER=root
      - FRONTEND_WORKER_COUNT=1
      - API_WORKER_COUNT=1
      - LICENSE_HOST=*************:18080
      - TZ=Asia/Shanghai
      - JAVA_VERSION=--java-version--
    volumes:
      - /log/tapdata---version--:/tapdata/apps/logs
      - ./docker-entrypoint.sh:/tapdata/docker-entrypoint.sh
      - ./agent.yml:/tapdata/apps/agent.yml
    healthcheck:
      test: ["CMD", "/bin/sh", "-c", "ps -ef | grep tapdata-agent.jar | grep -v \"grep tapdata-agent.jar\""]
      interval: 10s
      timeout: 1s
      start_period: 120s

networks:
  tapdata:
    external: true