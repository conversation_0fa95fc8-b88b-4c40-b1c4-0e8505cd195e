apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: tapdata
    version: --version--
  name: tapdata---version--
  namespace: dev
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: tapdata
      version: --version--
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: tapdata
        version: --version--
    spec:
      containers:
      - env:
        - name: MONGODB_CONNECTION_STRING
          value: 10.35.131.118:30154/tapdata---version--?authSource=admin
        - name: MONGODB_PASSWORD
          value: Gotapd8!
        - name: MONGODB_USER
          value: root
        - name: FRONTEND_WORKER_COUNT
          value: "1"
        - name: API_WORKER_COUNT
          value: "1"
        image: asia-docker.pkg.dev/crypto-reality-377106/tapdata/tapdata-enterprise:--image-tag--
        imagePullPolicy: IfNotPresent
        livenessProbe:
          failureThreshold: 600
          exec:
            command:
              - /bin/sh
              - -c
              - 'ps -ef | grep tapdata-agent.jar | grep -v "grep tapdata-agent.jar"'
          initialDelaySeconds: 120
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 1
        name: tapdata
        ports:
        - containerPort: 3080
          protocol: TCP
        - containerPort: 3030
          protocol: TCP
        readinessProbe:
          failureThreshold: 600
          exec:
            command:
              - /bin/sh
              - -c
              - 'ps -ef | grep tapdata-agent.jar | grep -v "grep tapdata-agent.jar"'
          initialDelaySeconds: 120
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 1
        resources:
          limits:
            cpu: "4"
            ephemeral-storage: 10Gi
            memory: 8Gi
          requests:
            cpu: "4"
            ephemeral-storage: 10Gi
            memory: 8Gi
        securityContext:
          capabilities:
            drop:
            - NET_RAW
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /tapdata/docker-entrypoint.sh
          name: volume-f8upz6
          readOnly: true
          subPath: docker-entrypoint.sh
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext:
        seccompProfile:
          type: RuntimeDefault
      terminationGracePeriodSeconds: 30
      volumes:
      - configMap:
          defaultMode: 493
          items:
          - key: entry_point
            path: docker-entrypoint.sh
          - key: auto_discovery
            path: auto-discovery.sh
          name: tapdata-mount-entrypoint
        name: volume-f8upz6
      - name: kube-api-access-cnqsk
        projected:
          defaultMode: 420
          sources:
          - serviceAccountToken:
              expirationSeconds: 3607
              path: token
          - configMap:
              items:
              - key: ca.crt
                path: ca.crt
              name: kube-root-ca.crt
          - downwardAPI:
              items:
              - fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.namespace
                path: namespace
---
apiVersion: v1
kind: Service
metadata:
  name: tapdata---version--
  namespace: dev
  annotations:
    cloud.google.com/l4-rbs: "enabled"
  labels:
    app: tapdata---version--
spec:
  type: LoadBalancer
  externalTrafficPolicy: Cluster
  ports:
    - name: api
      port: 30635
      protocol: TCP
      targetPort: 3080
    - name: web
      port: 30273
      protocol: TCP
      targetPort: 3030
    - name: websocket
      port: 31271
      protocol: TCP
      targetPort: 8246
  selector:
    app: tapdata
    version: --version--
