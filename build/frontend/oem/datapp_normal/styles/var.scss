//@use '~@tap/assets/styles/var' as *;
@import '~@tap/assets/styles/var';

$color: map-merge($color, (
        primary: #BD2828
));

//$color: (
//  // primary: #337dff,
//        primary: #BD2828,
//        dprimary: #0e42d2,
//        lprimary: #4080ff,
//        disprimary: #94bfff,
//        success: #00B42A,
//        lsuccess: #E8FFEA,
//        danger: #f53f3f,
//        warning: #ff7d00,
//        info: #75849b,
//        linfo: #e9edf2,
//        disable: #c9cdd4,
//        white: #fff,
//        tag: #008eff,
//        secondary: #ff7d00,
//        submenu: #f1f2f4
//);

$--color-primary: map-get($color, primary); // #2c65ff

//@import '~@tap/assets/styles/utilities';
//@import '~@/styles/overrides';
