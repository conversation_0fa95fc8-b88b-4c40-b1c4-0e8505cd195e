#!/bin/bash

SCRIPT_BASE_DIR=$(dirname "$0")
. "$SCRIPT_BASE_DIR/log.sh"
PROJECT_ROOT_DIR=$(cd "$SCRIPT_BASE_DIR/.." && pwd)
###### HOST CONFIG ######
HOST_IP="*************"
USER="cicd"
KEYFILE=$(cd "$SCRIPT_BASE_DIR" && pwd)/id_rsa
SSH_CMD="ssh -i $KEYFILE -o StrictHostKeyChecking=no $USER@$HOST_IP"
###### END HOST CONFIG ######
###### Gogs CONFIG ######
SSH_URI="ssh://git@**************:10022/tapdata"
###### END Gogs CONFIG ######
DOCKER_REGISTRY="harbor.internal.tapdata.io/tapdata/tapdata/tapdata-enterprise"

# deploy way, docker compose or kubernetes
DEPLOY_WAY="docker-compose"
# version of tapdata
VERSION=""
# get ip and port of tapdata
GET_IP_PORT="false"
# deploy a new one to test
DEPLOY="false"
# upgrade a old one to test
UPGRADE="false"
# upgrade environment
UPGRADE_ENV=""
# delete environment
DELETE_ENV=""
# delete environment by tag
DELETE_ENV_TAG="true"
# delete environment by name
DELETE_ENV_NAME="false"
# upgrade code
UPGRADE_CODE="false"
# upgrade code from path
UPGRADE_CODE_PATH=""
# Gogs Project Name
GOGS_PROJECT_NAME=""
# sync agent image
SYNC_AGENT_IMAGE="false"
# agent product
PRODUCT="dfs"
WAIT_MINUTES=60
# gitee account name
GITE_ACCOUNT_NAME="tapdata_1"
# java version
JAVA_VERSION="java8"

for i in "$@"
  do
  case $i in
    -w=*|--deploy-way=*)
    DEPLOY_WAY="${i#*=}"
    shift # past argument=value
    ;;
    -v=*|--version=*)
    VERSION="${i#*=}"
    shift # past argument=value
    ;;
    --get-ip-port=*)
    GET_IP_PORT="${i#*=}"
    shift # past argument=value
    ;;
    --deploy=*)
    DEPLOY="${i#*=}"
    shift # past argument=value
    ;;
    --upgrade=*)
    UPGRADE="${i#*=}"
    shift # past argument=value
    ;;
    --upgrade-env=*)
    UPGRADE_ENV="${i#*=}"
    shift # past argument=value
    ;;
    --delete-env=*)
    DELETE_ENV="${i#*=}"
    shift # past argument=value
    ;;
    --delete-env-tag=*)
    DELETE_ENV_TAG="${i#*=}"
    shift # past argument=value
    ;;
    --delete-env-name=*)
    DELETE_ENV_NAME="${i#*=}"
    shift # past argument=value
    ;;
    --upgrade-code=*)
    UPGRADE_CODE="${i#*=}"
    shift # past argument=value
    ;;
    --upgrade-code-path=*)
    UPGRADE_CODE_PATH="${i#*=}"
    shift # past argument=value
    ;;
    --gogs-project-name=*)
    GITE_PROJECT_NAME="${i#*=}"
    shift # past argument=value
    ;;
    --sync-agent-image=*)
    SYNC_AGENT_IMAGE="${i#*=}"
    shift
    ;;
    --wait_minutes=*)
    WAIT_MINUTES="${i#*=}"
    shift
    ;;
    --gitee-project-name=*)
    GITE_PROJECT_NAME="${i#*=}"
    shift
    ;;
    --java-version=*)
    JAVA_VERSION="${i#*=}"
    shift
    ;;
    *)
    echo "Unknown option: $i"
    exit 1
    ;;
  esac
done

upgrade_code() {
  if [[ $GITE_PROJECT_NAME == "tapdata-enterprise-web" ]]; then
    GITE_PROJECT_NAME="tapdata-web"
  fi
  info "Start upgrade code"
  info "Sync code to gitee, project name: $GITE_PROJECT_NAME"
  force_sync_project=$(curl "https://api.gitee.com/enterprises/********/projects/$GITE_ACCOUNT_NAME%2F$GITE_PROJECT_NAME/force_sync_project?qt=path" \
  -H 'Accept: application/json, text/plain, */*' \
  -H 'Accept-Language: zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-TW;q=0.6' \
  -H 'Cache-Control: no-cache' \
  -H 'Connection: keep-alive' \
  -H 'Content-Type: application/json' \
  -b 'user_locale=zh-CN; sensorsdata2015jssdkchannel=%7B%22prop%22%3A%7B%22_sa_channel_landing_url%22%3A%22%22%7D%7D; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%2218fe3d1c02c3ce-04063bb2be54fb8-1a525637-1484784-18fe3d1c02d2aed%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E8%87%AA%E7%84%B6%E6%90%9C%E7%B4%A2%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC%22%2C%22%24latest_referrer%22%3A%22https%3A%2F%2Fwww.google.com%2F%22%7D%2C%22%24device_id%22%3A%2218e2d1f7053b1c-04c4294c10035f-1d525637-1484784-18e2d1f70541252%22%7D; sl-session=YalIOBokwWdCkxZzMbIi0A==; BEC=1f1759df3ccd099821dcf0da6feb0357; csrf_token=tTpG4g5EOq6PIvJSJNbaLvb%2FG5UDbWTH4eOQAL4n12PnhjV%2BFCiOZ0ZbB%2BoTeOEwQghTqaH9I7EJarjT7DNaIw%3D%3D; gitee-session-n=cUJKMERsMWJsQVZPVGphaEtMZ2pTUVoxak9QK0VWeDVCckdkVjdzK3hhQzNYNEVRVjZaSjd0TWd5NWdyWlZrSEZRaUpwQzJ5cUNxaEh0cUwvcWhQajl5c3JGZ1RrWE1LSGpnY0lXR3BrcVJKTUFOS2hxUnJZQmUxU3ZwSlA0Y2ZMNThHNm45YUEwTHpscTRUUUd1SS9lVk04MWZCRjVJeXovV2hzR1F0YUllUkNVYjA5bjRUT2V3b0lwR1dMQm50OGJvOVZMelp4U1k0OVM1VGlIaWRGNHU1L1JpQTJRNWxmdW9JczNSQ3dGZmFxRDdmYnNhN1Eyd01SU25aQ3A2U0dyWkl2OE5uZUFFSkF0eXp0M2VhSWlsS0tGQnl1b0FQR293SFRIMWk2WlJwRVk2UXVoOFMxRGZmRHhVS2JRNXRTZWtlU3RuY093MXhGZHRndzkva05wVWR6SGhteDhQUU95eEI2RThKa3dLSzJONzE5TVlya3I1bWZYaG1Bcys3ejlEbkpjNnJOS3BwVTlLbXpCbjlHWHNScUJGcHYwdFg5dDFGRTRPYkVqM0VVL1AyY0tGc3BOYlZRSktNR2JaNWljTnc0ZHRXSWF1SUV4TUo0dm1QcmZqaDhsN3VNSDNjUkcyVTl3SEYzc3hhYVZESnhaOFVzdGxSU29SSG5vTXp6SCtpUXd4SlY2UWFQdStnSXlKTnpUK2VxMkRlOG8wd2RlcmVrMWNkZ094MC9kTkVKQndoUlJxdHRIV2d3YVNxc3A2bWI3OVl5RDBMd2hMNTVVL0pSVy9EaDdlZkVJeWJLNHZ6ZWJhc1BKVT0tLW40T3hSQUJoS2VGaUxjeC92OXdvb0E9PQ%3D%3D--3770f5ee98281211c9cac2996d3639251df04137' \
  -H 'Origin: https://e.gitee.com' \
  -H 'Pragma: no-cache' \
  -H 'Referer: https://e.gitee.com/' \
  -H 'Sec-Fetch-Dest: empty' \
  -H 'Sec-Fetch-Mode: cors' \
  -H 'Sec-Fetch-Site: same-site' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  --data-raw $'{"prune":0,"sync_wiki":1,"user_sync_code":"dreamcoin1998","password_sync_code":"****************************************"}' | jq .id)
  echo "force_sync_project: $force_sync_project"
  if [[ $? -eq 0 && ! -z $force_sync_project ]]; then
    info "Trigger force sync project success"
  else
    error "Trigger force sync project failed"
  fi

  success=false
  count=0
  while [[ $count -lt 10 ]]; do
    count=$((count+1))
    sleep 10
    check_force_sync_status=$(curl "https://api.gitee.com/enterprises/********/projects/$GITE_ACCOUNT_NAME%2F$GITE_PROJECT_NAME/check_fetch?qt=path" \
      -H 'Accept: application/json, text/plain, */*' \
      -H 'Accept-Language: zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-TW;q=0.6' \
      -H 'Cache-Control: no-cache' \
      -H 'Connection: keep-alive' \
      -H 'Content-Type: application/x-www-form-urlencoded' \
      -b 'user_locale=zh-CN; sensorsdata2015jssdkchannel=%7B%22prop%22%3A%7B%22_sa_channel_landing_url%22%3A%22%22%7D%7D; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%2218fe3d1c02c3ce-04063bb2be54fb8-1a525637-1484784-18fe3d1c02d2aed%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E8%87%AA%E7%84%B6%E6%90%9C%E7%B4%A2%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC%22%2C%22%24latest_referrer%22%3A%22https%3A%2F%2Fwww.google.com%2F%22%7D%2C%22%24device_id%22%3A%2218e2d1f7053b1c-04c4294c10035f-1d525637-1484784-18e2d1f70541252%22%7D; sl-session=YalIOBokwWdCkxZzMbIi0A==; BEC=1f1759df3ccd099821dcf0da6feb0357; csrf_token=tTpG4g5EOq6PIvJSJNbaLvb%2FG5UDbWTH4eOQAL4n12PnhjV%2BFCiOZ0ZbB%2BoTeOEwQghTqaH9I7EJarjT7DNaIw%3D%3D; gitee-session-n=ZHp4RFIxQWtVMW5WcFFpdmtsNjF0dHAyZm9IMHV2L05Geit0S0szUkFNc3NMVWc5S1BlWXpOMk5mZlBKMzdoQzRvZG13aWcxYUhHemYvR1c2MnRyVTVWb0dMNzNVZldLMHRRSEZoWS9KWVp2azhyVWswc2VRY20wdUkzeTVDa2NKa3hIbjZzYmpYYTdQeTRQdlYzUlAvTGZPOTRSRVFTTkRScnhKR3pjbG5mc3JJaGV4UUFPOWQxTlRySkYvNlhoYm5aaHBQM0p1SDA1UUc3VkFVeklrYVhGV3NGM1RJeHIyRU9iN2RMT3Zic05wUjlybVRvaFhoMVlxMEtJdGc5VkNSVjYzK2p4OUV3V1A2bkcwOE9PK0NITjJSdVlFNkVid3NFMGp1M3BlT1JRaUlQOEJHbklaZnNnL0MxMEt4R3g4aW9BK3h5eDBVcHJaOWt6U0hPa2UwTkJIT3NIdWpZa29wamt5MCt1Z2c3dnJzK0duSS9pL3EwdlRyazdTbEo3a1hUbG5UcFVuNjA5NlNaNG5kWVBuT3F5R2hCSGR1SFJDaXRlYUVGNXFGd1Z1czVJbUUwdVN5TnhZa1lJT1E0eU5IYlRJWlBVRk1uTkN1ZTdCY1RxSnJtekhyU01pUUZ6Z2hwZmxSemxqMUVwRGFESDhYTzJnalhnak5renVZRWowM3lCWGNjbzJzSU1qZkw1bklBVlNpV2ZqaDdQcUw1cTNkNlltaTUraXZLU1hUdEw1UU5Kc01jYWk2UWpiNVlwcy9kTm9tOFJHdDFtSlRxR3piY2Y0MXRoSTNSdzZBU242cHFPYjFuSnBxZz0tLTVPazRPdWxxcmlnVnVCT2dKaGEyN1E9PQ%3D%3D--3b19f5da1ec921305c9810ba7c463fefb49eee9a' \
      -H 'Origin: https://e.gitee.com' \
      -H 'Pragma: no-cache' \
      -H 'Referer: https://e.gitee.com/' \
      -H 'Sec-Fetch-Dest: empty' \
      -H 'Sec-Fetch-Mode: cors' \
      -H 'Sec-Fetch-Site: same-site' \
      -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
      -H 'sec-ch-ua: "Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"' \
      -H 'sec-ch-ua-mobile: ?0' \
      -H 'sec-ch-ua-platform: "macOS"' )
    echo "check_force_sync_status: $check_force_sync_status"
    if [[ $(echo "$check_force_sync_status" | jq '.error_msg') == '"代码拉取失败"' ]]; then
      warn "代码拉取失败，继续监听"
    elif [[ -z $check_force_sync_status || $(echo "$check_force_sync_status" | jq '.in_fetch') == "false" ]]; then
      info "Check force sync status success"
      success=true
      break
    else
      warn "Check force sync status failed, retry..."
    fi
  done
  if [[ $success == "false" ]]; then
    error "Check force sync status failed"
  fi
  info "Sync code to gitee success"
}

format_version_name() {
  version_name=$1
  if [[ "$version_name" == "" ]]; then
    error "Version name is empty"
  else
    VERSION_FORMATTED=$(echo $version_name | tr 'A-F' 'a-f' | sed 's/\.//g')
  fi
}

# get ip and port in deploy environment
get_ip_and_port() {
  # format version, version like v3.5.8-dfad7dsfad format to v358-dfad7dsfad
  notice "*** Start get ip and port ***"
  info "Format version"
  format_version_name $VERSION
  info "Version: tapdata-$VERSION_FORMATTED"
  # change id_rsa permission
  info "Change id_rsa permission"
  chmod 600 $KEYFILE
  ###  get ip and port
  # TODO: get ip and port from kubernetes
  # get ip and port from docker-compose
  if [[ "$DEPLOY_WAY" == "docker-compose" ]]; then
    # get ip and port from docker-compose
    info "Get ip and port from docker-compose"
    PORT=$(ssh -o StrictHostKeyChecking=no -i $KEYFILE $USER@$HOST_IP "sudo docker port tapdata-$VERSION_FORMATTED 3030 | awk -F: '{print \$2}' | grep -v '^$'")
    if [[ "$PORT" == "" ]]; then
      error "Get ip and port failed"
    else
      info "Get ip and port success"
      notice "IP: $HOST_IP"
      notice "PORT: $PORT"
      echo "$HOST_IP:$PORT" > .service_ip_port
    fi
  else
    error "Unknown deploy way: $DEPLOY_WAY"
  fi
}

# get port unused in deploy environment
get_unused_port() {
  info "Get unused port 3030"
  PORT_3030=$($SSH_CMD 'bash -c "comm -23 <(seq 30000 31000 | sort) <(ss -tan | awk '\''{print \$4}'\'' | cut -d':' -f2 | sort -u) | awk '\''NR==1'\''"')
  if [[ "$PORT_3030" == "" ]]; then
    error "Get unused port failed"
  else
    info "Get unused port success"
    notice "PORT: $PORT_3030"
  fi
  info "Get unused port 3080"
  PORT_3080=$($SSH_CMD 'bash -c "comm -23 <(seq 30000 31000 | sort) <(ss -tan | awk '\''{print \$4}'\'' | cut -d':' -f2 | sort -u) | awk '\''NR==2'\''"')
  if [[ "$PORT_3080" == "" ]]; then
    error "Get unused port failed"
  else
    info "Get unused port success"
    notice "PORT: $PORT_3080"
  fi
}

start_deploy() {
  $SSH_CMD "test -d /app/tapdata-$VERSION_FORMATTED || mkdir -p /app/tapdata-$VERSION_FORMATTED"
  scp -i $KEYFILE -o StrictHostKeyChecking=no \
      $PROJECT_ROOT_DIR/build/docker-compose/* $USER@$HOST_IP:/app/tapdata-$VERSION_FORMATTED/
  $SSH_CMD "chmod +x /app/tapdata-$VERSION_FORMATTED/docker-entrypoint.sh"
  $SSH_CMD "cd /app/tapdata-$VERSION_FORMATTED && sudo docker compose up -d"
}

deploy_by_docker_compose() {
  info "*** Start deploy by docker-compose ***"
  info "Change id_rsa permission"
  chmod 600 $KEYFILE
  info "Format version"
  format_version_name $VERSION  # set $VERSION_FORMATTED
  info "Version: $VERSION_FORMATTED"
  info "Get unused port"
  get_unused_port  # set $PORT_3030 and $PORT_3080
  info "Render docker-compose.yml"
  if [[ `uname` == "Linux" ]]; then
    sed -i "s/--port3030--/$PORT_3030/g" $PROJECT_ROOT_DIR/build/docker-compose/docker-compose.yaml
    sed -i "s/--port3080--/$PORT_3080/g" $PROJECT_ROOT_DIR/build/docker-compose/docker-compose.yaml
    sed -i "s/--version--/$VERSION_FORMATTED/g" $PROJECT_ROOT_DIR/build/docker-compose/docker-compose.yaml
    sed -i "s/--image-tag--/$VERSION/g" $PROJECT_ROOT_DIR/build/docker-compose/docker-compose.yaml
    sed -i "s/--java-version--/$JAVA_VERSION/g" $PROJECT_ROOT_DIR/build/docker-compose/docker-compose.yaml
  elif [[ `uname` == "Darwin" ]]; then
    sed -i '' "s/--port3030--/$PORT_3030/g" $PROJECT_ROOT_DIR/build/docker-compose/docker-compose.yaml
    sed -i '' "s/--port3080--/$PORT_3080/g" $PROJECT_ROOT_DIR/build/docker-compose/docker-compose.yaml
    sed -i '' "s/--version--/$VERSION_FORMATTED/g" $PROJECT_ROOT_DIR/build/docker-compose/docker-compose.yaml
    sed -i '' "s/--image-tag--/$VERSION/g" $PROJECT_ROOT_DIR/build/docker-compose/docker-compose.yaml
    sed -i '' "s/--java-version--/$JAVA_VERSION/g" $PROJECT_ROOT_DIR/build/docker-compose/docker-compose.yaml
  else
    error "Unknown OS: `uname`"
  fi
  if [[ $? -ne 0 ]]; then
    error "Render docker-compose.yml failed"
  else
    info "Render docker-compose.yml success"
  fi
  info "Copy docker-compose.yml to deploy server"
  start_deploy
  info "Watch deploy status..."
  $SSH_CMD "sudo docker logs -f tapdata-$VERSION_FORMATTED &" &
  for i in $(seq 1 $((WAIT_MINUTES*2))); do
    sleep 30
    $SSH_CMD "sudo docker ps | grep tapdata-$VERSION_FORMATTED | grep '(healthy)'" && break
    warn "tapdata-$VERSION_FORMATTED not Running."
  done
  $SSH_CMD "sudo docker ps | grep tapdata-$VERSION_FORMATTED | grep '(healthy)'"
  if [[ $? -ne 0 ]]; then
    error "Deploy failed"
  else
    info "Deploy success"
  fi
  echo "$HOST_IP:$PORT_3030" > .service_ip_port
}

upgrade_by_docker_compose() {
  info "*** Start upgrade by docker-compose ***"
  info "Change id_rsa permission"
  chmod 600 $KEYFILE
  info "Format version"
  format_version_name $VERSION  # set $VERSION_FORMATTED
  info "Version: $VERSION_FORMATTED"
  info "Copy remote docker-compose.yml to local temp dir"
  if [[ $UPGRADE_ENV == "" ]]; then
    error "UPGRADE_ENV is empty"
  fi
  mkdir -p $PROJECT_ROOT_DIR/temp/$UPGRADE_ENV/
  scp -i $KEYFILE -o StrictHostKeyChecking=no \
      $USER@$HOST_IP:/app/$UPGRADE_ENV/* /$PROJECT_ROOT_DIR/temp/$UPGRADE_ENV/
  info "Replace image tag in docker-compose.yml"
  cat $PROJECT_ROOT_DIR/temp/$UPGRADE_ENV/docker-compose.yaml | grep "image: tapdata-docker.pkg.coding.net/tapdata/tapdata/tapdata-enterprise:"
  if [[ $? -ne 0 ]]; then
    OLD_REGISTRY="$DOCKER_REGISTRY"
    OLD_TAG=$(grep "image: $DOCKER_REGISTRY:" "$PROJECT_ROOT_DIR/temp/$UPGRADE_ENV/docker-compose.yaml" | awk -F':' '{print $NF}')
  else
    OLD_REGISTRY="tapdata-docker.pkg.coding.net/tapdata/tapdata/tapdata-enterprise"
    OLD_TAG=$(grep "image: tapdata-docker.pkg.coding.net/tapdata/tapdata/tapdata-enterprise:" "$PROJECT_ROOT_DIR/temp/$UPGRADE_ENV/docker-compose.yaml" | awk -F':' '{print $NF}')
  fi
  if [[ "$OLD_TAG" == "" ]]; then
    error "Get old tag failed"
  else
    info "Old tag: $OLD_TAG"
  fi
  info "Get old Java version from docker-compose.yml"
  # Look for JAVA_VERSION in environment section, matching the format: "      - JAVA_VERSION=java8"
  OLD_JAVA_VERSION_LINE=$(grep -E '^ *- *JAVA_VERSION=' "$PROJECT_ROOT_DIR/temp/$UPGRADE_ENV/docker-compose.yaml")
  if [[ -z "$OLD_JAVA_VERSION_LINE" ]]; then
      warn "Could not find JAVA_VERSION in the existing docker-compose.yaml. Skipping Java version update."
      OLD_JAVA_VERSION=""
  else
      # Extract the value after the first '=' sign, and trim any whitespace
      OLD_JAVA_VERSION=$(echo "$OLD_JAVA_VERSION_LINE" | sed 's/^ *- *JAVA_VERSION=//' | tr -d ' ')
      info "Old Java version: $OLD_JAVA_VERSION"
  fi
  if [[ `uname` == "Linux" ]]; then
    sed -i "s|image: $OLD_REGISTRY:$OLD_TAG|image: $DOCKER_REGISTRY:$VERSION|g" $PROJECT_ROOT_DIR/temp/$UPGRADE_ENV/docker-compose.yaml
    if [[ -n "$OLD_JAVA_VERSION" ]]; then
        info "Updating Java version from $OLD_JAVA_VERSION to $JAVA_VERSION"
        # Replace the line with proper indentation and dash
        sed -i "s|^ *- *JAVA_VERSION=.*|      - JAVA_VERSION=$JAVA_VERSION|g" $PROJECT_ROOT_DIR/temp/$UPGRADE_ENV/docker-compose.yaml
    fi
  elif [[ `uname` == "Darwin" ]]; then
    sed -i '' "s|image: $OLD_REGISTRY:$OLD_TAG|image: $DOCKER_REGISTRY:$VERSION|g" $PROJECT_ROOT_DIR/temp/$UPGRADE_ENV/docker-compose.yaml
    if [[ -n "$OLD_JAVA_VERSION" ]]; then
        info "Updating Java version from $OLD_JAVA_VERSION to $JAVA_VERSION"
        # Replace the line with proper indentation and dash
        sed -i '' "s|^ *- *JAVA_VERSION=.*|      - JAVA_VERSION=$JAVA_VERSION|g" $PROJECT_ROOT_DIR/temp/$UPGRADE_ENV/docker-compose.yaml
    fi
  else
    error "Unknown OS: `uname`"
  fi
  info "Copy docker-compose.yml to deploy server"
  $SSH_CMD "sudo mkdir -p /tmp/$VERSION_FORMATTED/ && sudo chmod 777 /tmp/$VERSION_FORMATTED/"
  scp -i $KEYFILE -o StrictHostKeyChecking=no \
      $PROJECT_ROOT_DIR/temp/$UPGRADE_ENV/* $USER@$HOST_IP:/tmp/$VERSION_FORMATTED/
  info "Restart docker-compose"
  $SSH_CMD "sudo cp /tmp/$VERSION_FORMATTED/* /app/$UPGRADE_ENV/"
  $SSH_CMD "sudo rm -rf /tmp/$VERSION_FORMATTED"
  $SSH_CMD "cd /app/$UPGRADE_ENV && sudo docker compose up -d"
  info "Watch upgrade status..."
  $SSH_CMD "sudo docker logs -f $UPGRADE_ENV &" &
  for i in $(seq 1 $((WAIT_MINUTES*2))); do
    sleep 30
    $SSH_CMD "sudo docker ps | grep $UPGRADE_ENV | grep '(healthy)'" && break
    warn "$UPGRADE_ENV not Running."
  done
  $SSH_CMD "sudo docker ps | grep $UPGRADE_ENV | grep '(healthy)'"
  if [[ $? -ne 0 ]]; then
    error "Upgrade failed"
  else
    info "Upgrade success"
  fi
  echo "$HOST_IP:$PORT_3030" > .service_ip_port
}

delete_env() {
  info "*** Start delete env ***"
  if [[ $DELETE_ENV == "" ]]; then
    error "DELETE_ENV is empty"
  else
    info "Delete env: $DELETE_ENV"
    if [[ $DELETE_ENV_TAG != "" ]]; then
      format_version_name $DELETE_ENV  # set $VERSION_FORMATTED
    elif [[ $DELETE_ENV_NAME != "" ]]; then
      VERSION_FORMATTED=$DELETE_ENV
    else
      error "DELETE_ENV_TAG and DELETE_ENV_NAME are empty"
    fi
    info "Delete env: tapdata-$VERSION_FORMATTED"
  fi
  info "Change id_rsa permission"
  chmod 600 $KEYFILE
  info "Delete env: tapdata-$VERSION_FORMATTED"
  $SSH_CMD "cd /app/tapdata-$VERSION_FORMATTED && sudo docker compose down"
  $SSH_CMD "rm -rf /app/tapdata-$VERSION_FORMATTED"
  # $SSH_CMD "rm -rf /log/tapdata-$VERSION_FORMATTED"
  if [[ $? -ne 0 ]]; then
    error "Delete env: tapdata-$VERSION_FORMATTED failed"
  else
    info "Delete env: tapdata-$VERSION_FORMATTED success"
  fi
}

sync_agent_image() {
  # sync agent image to target docker registry
  # 1. login to docker registry
  # 2. pull agent image from coding docker registry
  # 3. tag agent image to target docker registry
  # 4. push agent image to target docker registry
  ############# -Start Images list - #############
  SOURCE_IMAGE_TAG="tapdata-docker.pkg.coding.net/dfs/flow-engine/$PRODUCT-flow-engine:$VERSION"
  TARGET_IMAGE_TAGS="""registry.cn-beijing.aliyuncs.com/tapview/agent:$VERSION
  registry.cn-hongkong.aliyuncs.com/tapview/agent:$VERSION
  """
  ############# -End Images list - #############
  # 1. login to docker registry
  docker login -u images-1629288850206 -p 0446c95e2f4742b5947dfe781a812a218b72f0fa tapdata-docker.pkg.coding.net # login to coding
  docker login --username=tapdata_net registry.cn-hongkong.aliyuncs.com -p Gotapd8! # login to aliyun hongkong
  docker login --username=tapdata_net registry.cn-beijing.aliyuncs.com -p Gotapd8! # login to aliyun beijing
  # 2. docker buildx tag
  for TARGET_IMAGE_TAG in $TARGET_IMAGE_TAGS; do
    docker buildx imagetools create --tag $TARGET_IMAGE_TAG $SOURCE_IMAGE_TAG
  done
}

main() {
  if [[ "$GET_IP_PORT" == "true" ]]; then
    get_ip_and_port
  elif [[ "$DEPLOY" == "true" ]]; then
    if [[ "$DEPLOY_WAY" == "docker-compose" ]]; then
      deploy_by_docker_compose
    else
      error "Unknown deploy way: $DEPLOY_WAY"
    fi
  elif [[ "$UPGRADE" == "true" ]]; then
    if [[ "$DEPLOY_WAY" == "docker-compose" ]]; then
      upgrade_by_docker_compose
    else
      error "Unknown upgrade way: $UPGRADE_WAY"
    fi
  elif [[ "$DELETE_ENV" != "" ]]; then
    delete_env
  elif [[ "$UPGRADE_CODE" == "true" ]]; then
    # if [[ -z "$UPGRADE_CODE_PATH" ]]; then
    #   error "UPGRADE_CODE_PATH is empty"
    # elif [[ -z "$GOGS_PROJECT_NAME" ]]; then
    #   error "GOGS_PROJECT_NAME is empty"
    # fi
    upgrade_code
  elif [[ "$SYNC_AGENT_IMAGE" == "true" ]]; then
    sync_agent_image
  else
    error "Unknown action"
  fi
}

main
