[Unit]
Description=Tapdata MongoDB Database Server
Documentation=https://docs.mongodb.org/manual
After=network-online.target
Wants=network-online.target

[Service]
Type=forking
User=root
Group=root
Environment="MONGODB_PORT=27017"
Environment="REPLSET_NAME=tapdata"

# 设置工作目录和数据目录
WorkingDirectory=/opt/tapdata/mongodb
RuntimeDirectory=mongodb
RuntimeDirectoryMode=0755

# MongoDB 配置
ExecStart=/opt/tapdata/mongodb/bin/mongod \
    --replSet ${REPLSET_NAME} \
    --port ${MONGODB_PORT} \
    --dbpath /opt/tapdata/mongodb/data \
    --bind_ip_all \
    --fork \
    --logpath /opt/tapdata/mongodb/data/mongod.log

# 停止命令
ExecStop=/opt/tapdata/mongodb/bin/mongosh --port ${MONGODB_PORT} --eval "db.adminCommand({ shutdown: 1 })"

# 资源限制
LimitFSIZE=infinity
LimitCPU=infinity
LimitAS=infinity
LimitNOFILE=64000
LimitNPROC=64000

# 重启策略
Restart=always
RestartSec=30

[Install]
WantedBy=multi-user.target 