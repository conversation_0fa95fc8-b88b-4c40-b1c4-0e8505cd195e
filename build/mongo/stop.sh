#!/bin/bash

# load log.sh if exists
SCRIPT_DIR=$(dirname "$0")
if [ -f "$SCRIPT_DIR/../log.sh" ]; then
    . "$SCRIPT_DIR/../log.sh"
else
    # 如果没有log.sh，定义基本的日志函数
    info() { echo "[INFO] $1"; }
    error() { echo "[ERROR] $1"; }
    warn() { echo "[WARN] $1"; }
fi

MONGODB_PORT=27017
INSTALL_DIR="/opt/tapdata/mongodb"
MONGODB_DATA_DIR="$INSTALL_DIR/data"
SERVICE_NAME="tapdata-mongodb"

# 检查是否支持systemd
check_systemd_support() {
    if command -v systemctl >/dev/null 2>&1 && systemctl --version >/dev/null 2>&1; then
        return 0
    fi
    return 1
}

# 检查是否有root权限
check_root() {
    if [ "$(id -u)" != "0" ]; then
        error "此脚本需要root权限运行"
        error "请使用 sudo 或 root 用户运行"
        exit 1
    fi
}

# 检查MongoDB是否真的已停止
check_mongodb_really_stopped() {
    # 检查端口是否已释放
    if nc -z localhost $MONGODB_PORT 2>/dev/null; then
        return 1
    fi
    
    # 检查进程是否存在
    if pgrep -f "mongod.*--port $MONGODB_PORT" > /dev/null; then
        return 1
    fi
    
    return 0
}

# 使用mongosh优雅关闭MongoDB
stop_mongodb_gracefully() {
    info "正在尝试优雅关闭MongoDB..."
    if [ -f "$INSTALL_DIR/bin/mongosh" ]; then
        "$INSTALL_DIR/bin/mongosh" --port $MONGODB_PORT --eval "db.adminCommand({ shutdown: 1 })" >/dev/null 2>&1
        if [ $? -eq 0 ]; then
            info "MongoDB已成功关闭"
            return 0
        fi
    fi
    return 1
}

# 通过进程ID关闭MongoDB
stop_mongodb_by_pid() {
    info "正在通过进程ID关闭MongoDB..."
    local pid=""
    
    # 首先尝试从lock文件获取PID
    if [ -f "$MONGODB_DATA_DIR/mongod.lock" ]; then
        pid=$(cat "$MONGODB_DATA_DIR/mongod.lock")
    fi
    
    # 如果lock文件中没有有效PID，尝试通过端口查找
    if [ -z "$pid" ] || ! kill -0 $pid 2>/dev/null; then
        pid=$(lsof -i:$MONGODB_PORT -t)
    fi
    
    # 如果还是没找到，尝试通过进程名查找
    if [ -z "$pid" ]; then
        pid=$(pgrep -f "mongod.*--port $MONGODB_PORT")
    fi

    if [ -n "$pid" ]; then
        info "找到MongoDB进程 PID: $pid"
        if kill -TERM $pid 2>/dev/null; then
            info "已发送终止信号到MongoDB进程"
            # 等待进程结束
            for i in {1..30}; do
                if ! kill -0 $pid 2>/dev/null; then
                    info "MongoDB进程已终止"
                    return 0
                fi
                sleep 1
            done
            # 如果进程仍然存在，使用SIGKILL
            if kill -KILL $pid 2>/dev/null; then
                info "已强制终止MongoDB进程"
                return 0
            fi
        fi
    fi
    return 1
}

# 停止MongoDB服务（systemd方式）
stop_mongodb_systemd() {
    info "正在停止MongoDB服务..."
    
    # 检查服务是否在运行
    if ! systemctl is-active $SERVICE_NAME &>/dev/null; then
        info "MongoDB服务未在运行"
        return 0
    fi
    
    # 停止服务
    systemctl stop $SERVICE_NAME
    
    # 等待服务完全停止
    local max_attempts=30
    local attempt=1
    while [ $attempt -le $max_attempts ]; do
        if ! systemctl is-active $SERVICE_NAME &>/dev/null; then
            info "MongoDB服务已停止"
            return 0
        fi
        info "等待服务停止... ($attempt/$max_attempts)"
        sleep 1
        attempt=$((attempt + 1))
    done
    
    error "MongoDB服务停止超时"
    return 1
}

# 清理资源
cleanup() {
    info "正在清理资源..."
    
    # 清理锁文件
    if [ -f "$MONGODB_DATA_DIR/mongod.lock" ]; then
        rm -f "$MONGODB_DATA_DIR/mongod.lock"
        info "已清理锁文件"
    fi
    
    # 清理socket文件（如果存在）
    local socket_file="/tmp/mongodb-$MONGODB_PORT.sock"
    if [ -S "$socket_file" ]; then
        rm -f "$socket_file"
        info "已清理socket文件"
    fi
}

# 主函数
main() {
    check_root
    
    # 检查是否支持systemd
    if check_systemd_support && systemctl list-unit-files | grep -q $SERVICE_NAME.service; then
        info "使用systemd停止MongoDB服务"
        if stop_mongodb_systemd; then
            cleanup
            info "MongoDB服务已成功停止"
            exit 0
        else
            error "MongoDB服务停止失败"
            error "请检查日志: journalctl -u $SERVICE_NAME -n 50"
            exit 1
        fi
    else
        info "使用二进制方式停止MongoDB"
        # 首先尝试优雅关闭
        if stop_mongodb_gracefully; then
            sleep 2  # 等待进程完全退出
            if check_mongodb_really_stopped; then
                cleanup
                info "MongoDB已成功停止"
                exit 0
            fi
        fi

        # 如果优雅关闭失败，尝试通过PID关闭
        if stop_mongodb_by_pid; then
            sleep 2  # 等待进程完全退出
            if check_mongodb_really_stopped; then
                cleanup
                info "MongoDB已成功停止"
                exit 0
            fi
        fi

        # 最后检查一次
        if check_mongodb_really_stopped; then
            cleanup
            info "MongoDB已成功停止"
            exit 0
        else
            error "MongoDB停止失败"
            error "请尝试手动终止进程："
            error "1. ps aux | grep mongod"
            error "2. kill -9 <进程ID>"
            exit 1
        fi
    fi
}

main