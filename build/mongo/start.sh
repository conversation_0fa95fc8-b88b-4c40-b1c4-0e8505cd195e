#!/bin/bash

# load log.sh if exists
SCRIPT_DIR=$(dirname "$0")
if [ -f "$SCRIPT_DIR/../log.sh" ]; then
    . "$SCRIPT_DIR/../log.sh"
else
    # 如果没有log.sh，定义基本的日志函数
    info() { echo "[INFO] $1"; }
    error() { echo "[ERROR] $1"; }
    warn() { echo "[WARN] $1"; }
fi

MONGODB_VERSION="6.0.12"
MONGOSH_VERSION="2.1.1"
MONGODB_PORT=27017
INSTALL_DIR="$SCRIPT_DIR/mongodb"
MONGODB_DATA_DIR="$INSTALL_DIR/data"
REPLSET_NAME="tapdata"
SERVICE_NAME="tapdata-mongodb"
SERVICE_FILE="/etc/systemd/system/$SERVICE_NAME.service"

# 检查是否支持systemd
check_systemd_support() {
    if command -v systemctl >/dev/null 2>&1 && systemctl --version >/dev/null 2>&1; then
        return 0
    fi
    return 1
}

# 检查MongoDB是否正在运行
check_mongodb_running() {
    # 检查进程是否在运行
    if pgrep -f "mongod.*--port $MONGODB_PORT" > /dev/null; then
        info "发现端口 $MONGODB_PORT 上有MongoDB进程在运行"
        return 0
    fi

    # 如果进程未运行，但存在lock文件，说明可能是异常退出
    if [ -f "$MONGODB_DATA_DIR/mongod.lock" ]; then
        warn "发现mongod.lock文件但没有对应的MongoDB进程，可能是由于异常退出导致"
        warn "正在清理lock文件..."
        rm -f "$MONGODB_DATA_DIR/mongod.lock"
    fi

    return 1  # 未运行
}

# 检查是否有root权限
check_root() {
    if [ "$(id -u)" != "0" ]; then
        error "此脚本需要root权限运行"
        error "请使用 sudo 或 root 用户运行"
        exit 1
    fi
}

# 检查系统架构
check_arch() {
    arch=$(uname -m)
    if [ "$arch" = "x86_64" ]; then
        MONGODB_PACKAGE="mongodb-linux-x86_64-6.0.12.tgz"
        MONGOSH_PACKAGE="mongosh-${MONGOSH_VERSION}-linux-x64.tgz"
        info "检测到x86_64架构"
    elif [ "$arch" = "aarch64" ] || [ "$arch" = "arm64" ]; then
        MONGODB_PACKAGE="mongodb-linux-aarch64-6.0.12.tgz"
        MONGOSH_PACKAGE="mongosh-${MONGOSH_VERSION}-linux-arm64.tgz"
        info "检测到ARM64架构"
    else
        error "不支持的系统架构: $arch"
        exit 1
    fi
}

# 检查并安装依赖
check_and_install_dependencies() {
    info "检查系统依赖..."
    
    if command -v apt-get &> /dev/null; then
        PKG_MANAGER="apt-get"
        INSTALL_CMD="apt-get install -y"
        PACKAGES="libcurl4 openssl liblzma5 libssl1.1"
    elif command -v yum &> /dev/null; then
        PKG_MANAGER="yum"
        INSTALL_CMD="yum install -y"
        PACKAGES="openssl-libs xz-libs"
    elif command -v dnf &> /dev/null; then
        PKG_MANAGER="dnf"
        INSTALL_CMD="dnf install -y"
        PACKAGES="openssl-libs xz-libs"
    else
        error "不支持的包管理器。请手动安装依赖。"
        exit 1
    fi
    
    info "使用包管理器: $PKG_MANAGER"
    
    # 对于apt系统，需要先更新包索引
    if [ "$PKG_MANAGER" = "apt-get" ]; then
        info "更新包索引..."
        apt-get update
        
        # 对于Debian/Ubuntu系统，可能需要添加旧版本的libssl
        if ! dpkg -l | grep -q libssl1.1; then
            info "安装 libssl1.1..."
            if [ ! -f libssl1.1_1.1.1f-1ubuntu2_amd64.deb ]; then
                wget http://archive.ubuntu.com/ubuntu/pool/main/o/openssl/libssl1.1_1.1.1f-1ubuntu2_amd64.deb
            fi
            dpkg -i libssl1.1_1.1.1f-1ubuntu2_amd64.deb || true
            apt-get install -f -y
        fi
    fi
    
    # 安装依赖包
    info "安装所需包: $PACKAGES"
    $INSTALL_CMD $PACKAGES
    
    if [ $? -ne 0 ]; then
        error "安装依赖失败"
        exit 1
    fi
    
    info "依赖安装成功"
}

# 安装MongoDB
install_mongodb() {
    info "开始安装MongoDB..."
    
    # 创建安装目录
    if ! mkdir -p "$INSTALL_DIR/bin" "$MONGODB_DATA_DIR"; then
        error "创建安装目录失败"
        exit 1
    fi
    
    # 检查source目录中的MongoDB包
    if [ -f "$SCRIPT_DIR/source/$MONGODB_PACKAGE" ]; then
        PACKAGE_PATH="$SCRIPT_DIR/source/$MONGODB_PACKAGE"
    else
        error "在以下位置未找到MongoDB包:"
        error "  - $SCRIPT_DIR/source/$MONGODB_PACKAGE"
        error "请确保您已下载正确架构的MongoDB包并放置在source目录中"
        exit 1
    fi

    # 如果MongoDB二进制文件不存在，则安装
    if [ ! -f "$INSTALL_DIR/bin/mongod" ]; then
        info "使用MongoDB包: $PACKAGE_PATH"
        if ! tar xzf "$PACKAGE_PATH" -C "$INSTALL_DIR" --strip-components=1; then
            error "解压MongoDB包失败"
            exit 1
        fi
        info "MongoDB安装成功"
    else
        info "MongoDB已安装，跳过安装步骤"
    fi
    
    # 检查mongosh是否已安装
    if [ ! -f "$INSTALL_DIR/bin/mongosh" ]; then
        info "安装Mongosh..."
        if [ ! -f "$SCRIPT_DIR/source/$MONGOSH_PACKAGE" ]; then
            error "在source目录中未找到Mongosh包: $MONGOSH_PACKAGE"
            error "请确保您已下载正确架构的Mongosh包并放置在source目录中"
            exit 1
        fi
        
        # 创建临时目录进行解压
        TMP_DIR=$(mktemp -d)
        if ! tar xzf "$SCRIPT_DIR/source/$MONGOSH_PACKAGE" -C "$TMP_DIR"; then
            error "解压Mongosh包失败"
            rm -rf "$TMP_DIR"
            exit 1
        fi
        
        # 移动mongosh二进制文件
        if ! mv "$TMP_DIR"/mongosh-*/bin/mongosh "$INSTALL_DIR/bin/"; then
            error "移动mongosh二进制文件失败"
            rm -rf "$TMP_DIR"
            exit 1
        fi
        
        # 清理临时目录
        rm -rf "$TMP_DIR"
        info "Mongosh安装成功"
    else
        info "Mongosh已安装，跳过安装步骤"
    fi
    
    # 设置权限
    if ! chown -R root:root "$INSTALL_DIR"; then
        error "设置安装目录所有者失败"
        exit 1
    fi
    
    if ! chmod -R 755 "$INSTALL_DIR/bin"; then
        error "设置bin目录权限失败"
        exit 1
    fi
    
    if ! chmod 700 "$MONGODB_DATA_DIR"; then
        error "设置数据目录权限失败"
        exit 1
    fi
    
    info "权限设置完成"
}

# 安装systemd服务
install_systemd_service() {
    info "安装systemd服务..."
    
    # 获取INSTALL_DIR的绝对路径
    ABSOLUTE_INSTALL_DIR=$(cd "$INSTALL_DIR" && pwd)
    
    # 检查服务文件是否存在
    if [ ! -f "$SCRIPT_DIR/tapdata-mongodb.service" ]; then
        error "服务文件不存在: $SCRIPT_DIR/tapdata-mongodb.service"
        exit 1
    fi
    
    # 替换服务文件中的路径，确保使用绝对路径
    sed "s|/opt/tapdata/mongodb|$ABSOLUTE_INSTALL_DIR|g" "$SCRIPT_DIR/tapdata-mongodb.service" > "$SERVICE_FILE.tmp"
    
    # 验证临时文件是否创建成功
    if [ ! -f "$SERVICE_FILE.tmp" ]; then
        error "创建临时服务文件失败"
        exit 1
    fi
    
    # 移动临时文件到最终位置
    if ! mv "$SERVICE_FILE.tmp" "$SERVICE_FILE"; then
        error "移动服务文件失败"
        rm -f "$SERVICE_FILE.tmp"
        exit 1
    fi
    
    info "服务文件已安装到: $SERVICE_FILE"
    
    # 重新加载systemd配置
    if ! systemctl daemon-reload; then
        error "重新加载systemd配置失败"
        exit 1
    fi
    
    # 启用服务开机自启
    if ! systemctl enable $SERVICE_NAME; then
        error "启用服务开机自启失败"
        exit 1
    fi
    
    info "MongoDB服务已安装并设置为开机自启"
}

# 启动MongoDB副本集（非systemd方式）
start_mongodb_binary() {
    info "使用二进制方式启动MongoDB副本集..."
    
    # 检查端口是否被占用
    if nc -z localhost $MONGODB_PORT 2>/dev/null; then
        error "错误: 端口 $MONGODB_PORT 已被占用"
        error "请检查是否有其他MongoDB实例或应用程序正在使用该端口"
        exit 1
    fi

    if ! "$INSTALL_DIR/bin/mongod" \
        --replSet $REPLSET_NAME \
        --port $MONGODB_PORT \
        --dbpath "$MONGODB_DATA_DIR" \
        --bind_ip_all \
        --fork \
        --logpath "$MONGODB_DATA_DIR/mongod.log"; then
        error "启动MongoDB失败"
        error "检查mongod.log获取错误信息:"
        cat "$MONGODB_DATA_DIR/mongod.log"
        exit 1
    fi

    info "MongoDB已启动"
}

# 初始化副本集
initialize_replica_set() {
    info "等待MongoDB启动..."
    sleep 5
    
    # 检查mongosh是否可用
    if [ ! -f "$INSTALL_DIR/bin/mongosh" ]; then
        warn "mongosh未安装，跳过副本集初始化"
        return
    fi
    
    # 等待MongoDB启动完成
    local max_attempts=30
    local attempt=1
    while [ $attempt -le $max_attempts ]; do
        if "$INSTALL_DIR/bin/mongosh" --port $MONGODB_PORT --eval "db.adminCommand('ping')" &>/dev/null; then
            break
        fi
        sleep 1
        attempt=$((attempt + 1))
    done

    if [ $attempt -gt $max_attempts ]; then
        error "等待MongoDB启动超时"
        return
    fi

    # 检查副本集状态
    local rs_status
    rs_status=$("$INSTALL_DIR/bin/mongosh" --port $MONGODB_PORT --quiet --eval "rs.status().ok" 2>/dev/null || echo "0")
    
    if [ "$rs_status" != "1" ]; then
        info "初始化副本集..."
        "$INSTALL_DIR/bin/mongosh" --port $MONGODB_PORT --eval "rs.initiate({
            _id: '$REPLSET_NAME',
            members: [
                {_id: 0, host: 'localhost:$MONGODB_PORT'}
            ]
        })" >/dev/null
        
        info "等待副本集初始化完成..."
        sleep 5
    else
        info "副本集已经初始化，跳过初始化步骤"
    fi
}

# 主函数
main() {
    check_root
    
    # 检查MongoDB是否已在运行
    if check_mongodb_running; then
        info "MongoDB已经在运行中"
        exit 0
    fi
    
    # 安装必要的组件
    check_arch
    check_and_install_dependencies
    install_mongodb

    # 检查是否支持systemd
    if check_systemd_support; then
        info "检测到systemd支持，使用systemd管理MongoDB服务"
        install_systemd_service
        
        # 启动服务
        info "启动MongoDB服务..."
        systemctl start $SERVICE_NAME
        
        if [ $? -eq 0 ]; then
            info "MongoDB服务启动成功"
            initialize_replica_set
            info "MongoDB副本集已就绪！"
            info "连接字符串: mongodb://localhost:$MONGODB_PORT/?replicaSet=$REPLSET_NAME"
        else
            error "MongoDB服务启动失败"
            error "请检查日志: journalctl -u $SERVICE_NAME -n 50"
            exit 1
        fi
    else
        info "系统不支持systemd，使用二进制方式启动MongoDB"
        start_mongodb_binary
        initialize_replica_set
        info "MongoDB副本集已就绪！"
        info "查看服务状态: systemctl status $SERVICE_NAME"
        info "连接字符串: mongodb://localhost:$MONGODB_PORT/tapdata?replicaSet=$REPLSET_NAME"
    fi
}

main 