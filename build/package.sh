#!/bin/bash

# 加载日志函数
SCRIPT_BASE_DIR=$(dirname "$0")
. "$SCRIPT_BASE_DIR/log.sh"

# 默认值
TARGET_DIR=""
OS=""
INCLUDE_LICENSE=false
INCLUDE_JDK=false
INCLUDE_TAPCLI=false
INCLUDE_MONGODB=false
UPLOAD_TO_OSS=false  # 是否上传到oss
OSSUTIL_CONFIG_PATH=$SCRIPT_BASE_DIR/.ossutilconfig

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --target-dir)
            TARGET_DIR="$2"
            shift 2
            ;;
        --os)
            OS="$2"
            shift 2
            ;;
        --include-license)
            INCLUDE_LICENSE=true
            shift
            ;;
        --include-jdk)
            INCLUDE_JDK=true
            shift
            ;;
        --include-tapcli)
            INCLUDE_TAPCLI=true
            shift
            ;;
        --include-mongodb)
            INCLUDE_MONGODB=true
            shift
            ;;
        --upload-to-oss)
            UPLOAD_TO_OSS=true
            shift
            ;;
        *)
            error "未知参数: $1"
            exit 1
            ;;
    esac
done

if [[ $OS == "windows" ]]; then
    PLATFORM_INFO="win32-x86_64-latest"
elif [[ $OS == "ubuntu-20.04" || $OS == "ubuntu-22.04" || $OS == "centos-7" ]]; then
    PLATFORM_INFO="linux-x86_64-$OS-latest"
else
    error "Unsupported OS: $OS"
    exit 1
fi

if [[ $INCLUDE_JDK == "true" && $INCLUDE_MONGODB == "true" ]]; then
    ARTIFACT_NAME="tapdata-$PLATFORM_INFO"
    ARTIFACT_PATH="$ARTIFACT_NAME.tar.gz"
else
    ARTIFACT_NAME="tapdata-general-latest"
    ARTIFACT_PATH="$ARTIFACT_NAME.tar.gz"
fi

# 检查必需参数
# if [ -z "$TARGET_DIR" ] || [ -z "$OS" ]; then
#     error "Usage: $0 --target-dir <target_directory> --os <architecture> [--include-license] [--include-jdk] [--include-tapcli] [--include-mongodb]"
#     error "Example: $0 --target-dir /path/to/dir --os ubuntu-20.04 --include-license --include-jdk --include-tapcli --include-mongodb"
#     exit 1
# fi

echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd

install_mongodb() {
    info "Downloading MongoDB 6.0 packages..."

    # 创建mongodb安装包目录
    mkdir -p "$TARGET_DIR/components/"

    if [[ "$OS" == "ubuntu-20.04" ]]; then
        info "Downloading MongoDB 6.0 packages for ubuntu-20.04..."
        rsync --password-file=/tmp/rsync.passwd -vzrt --progress rsync://root@*************:873/data/enterprise-artifact/tools/mongodb/linux-ubuntu20-mongod "$TARGET_DIR/components/mongod"
    elif [[ "$OS" == "ubuntu-22.04" ]]; then
        info "Downloading MongoDB 6.0 packages for ubuntu-22.04..."
        rsync --password-file=/tmp/rsync.passwd -vzrt --progress rsync://root@*************:873/data/enterprise-artifact/tools/mongodb/linux-ubuntu22-mongod "$TARGET_DIR/components/mongod"
    elif [[ "$OS" == "centos-7" ]]; then
        info "Downloading MongoDB 6.0 packages for centos-7..."
        rsync --password-file=/tmp/rsync.passwd -vzrt --progress rsync://root@*************:873/data/enterprise-artifact/tools/mongodb/linux-centos7-mongod "$TARGET_DIR/components/mongod"
    elif [[ "$OS" == "windows" ]]; then
        info "Downloading MongoDB 6.0 packages for windows..."
        rsync --password-file=/tmp/rsync.passwd -vzrt --progress rsync://root@*************:873/data/enterprise-artifact/tools/mongodb/windows-mongod.exe "$TARGET_DIR/components/mongod.exe"
    fi

    info "MongoDB package downloaded successfully"
}

install_jdk() {
    info "Downloading JRE 1.8 packages..."

    if [[ "$OS" == "windows" ]]; then
        info "Downloading JRE 1.8 packages for windows..."
        rsync --password-file=/tmp/rsync.passwd -vzrt --progress rsync://root@*************:873/data/enterprise-artifact/tools/java/windows-jdk1.8 "$TARGET_DIR/lib/"
        mv "$TARGET_DIR/lib/windows-jdk1.8" "$TARGET_DIR/lib/jdk"
    else 
        info "Downloading JRE 1.8 packages for linux..."
        rsync --password-file=/tmp/rsync.passwd -vzrt --progress rsync://root@*************:873/data/enterprise-artifact/tools/java/linux-jdk1.8 "$TARGET_DIR/lib/"
        mv "$TARGET_DIR/lib/linux-jdk1.8" "$TARGET_DIR/lib/jdk"
    fi

    info "JRE package downloaded successfully"
}

install_lisence() {
    info "Downloading license..."
    curl -v -XPOST -H "Content-Type:application/json" http://*************:18080/ldap/login -d '{"password": "Gotapd8!", "uid": "license-temp"}'
    resp_json=$(curl -sb -v -H "Uid:license-temp" -H "Content-Type: application/json" -XPOST http://*************:18080/license -d '{"customer":"TapData","reason":"Default build-in license","valid_days":180,"version":"v3.22","engineLimit":1,"licenseType":"LITE","sid":""}')
    apt install -y jq
    if [[ -f $TARGET_DIR/.workDir ]]; then
        LISENCE_PATH=`cat $TARGET_DIR/.workDir`/license.txt
    else
        LISENCE_PATH=$TARGET_DIR/license.txt
    fi
    echo $resp_json | jq -r .data.content > $LISENCE_PATH

    info "License file downloaded successfully"
}

install_tapcli() {
    info "Downloading tapsh..."

    # windows -> tapflow-0.2.70-windows-x86_64.exe
    # ubuntu-20.04 ubuntu-22.04 -> tapflow-0.2.70-ubuntu-x86_64
    # centos-7 -> tapflow-0.2.70-centos-x86_64

    if [[ $OS == "windows" ]]; then
        rsync --password-file=/tmp/rsync.passwd -vzrt --progress rsync://root@*************:873/data/enterprise-artifact/gz/tapflow/latest/tapsh-latest-windows-x86_64.exe "$TARGET_DIR/tapsh.exe"
    elif [[ $OS == "ubuntu-20.04" || $OS == "ubuntu-22.04" ]]; then
        rsync --password-file=/tmp/rsync.passwd -vzrt --progress rsync://root@*************:873/data/enterprise-artifact/gz/tapflow/latest/tapsh-latest-ubuntu-x86_64 "$TARGET_DIR/tapsh"
    elif [[ $OS == "centos-7" ]]; then
        rsync --password-file=/tmp/rsync.passwd -vzrt --progress rsync://root@*************:873/data/enterprise-artifact/gz/tapflow/latest/tapsh-latest-centos-x86_64 "$TARGET_DIR/tapsh"
    else
        rsync --password-file=/tmp/rsync.passwd -vzrt --progress rsync://root@*************:873/data/enterprise-artifact/gz/tapflow/latest/tapsh-latest-linux-x86_64 "$TARGET_DIR/tapsh"
    fi
    info "tapcli downloaded successfully"
}

install_ossutil() {
    info "Installing ossutil..."

    current_path=$(pwd)
    wget 'https://gosspublic.alicdn.com/ossutil/1.7.15/ossutil-v1.7.15-linux-amd64.zip?spm=a2c4g.11186623.0.0.7d3f4edc1WL2Jt&file=ossutil-v1.7.15-linux-amd64.zip' -O /tmp/ossutils.zip
    unzip /tmp/ossutils.zip -d /tmp/ossutils
    path="/tmp/ossutils"
    cd $path/ossutil-* && mv ./* ../ && cd $current_path
    chmod -R u+x /tmp/ossutils

    info "ossutil installed successfully"
}

upload_to_oss() {
    info "Uploading to oss..."
    
    mv $UPLOAD_TO_OSS $ARTIFACT_PATH
    md5sum $ARTIFACT_PATH > $ARTIFACT_NAME.md5
    /tmp/ossutils/ossutil -c $OSSUTIL_CONFIG_PATH cp --force $ARTIFACT_PATH "oss://tapdata-cdn-beijing/package/lite/$ARTIFACT_PATH"
    /tmp/ossutils/ossutil -c $OSSUTIL_CONFIG_PATH cp --force $ARTIFACT_NAME.md5 "oss://tapdata-cdn-beijing/package/lite/$ARTIFACT_NAME.md5"

    info "Uploaded to oss successfully"
}

if [[ "$INCLUDE_MONGODB" == "true" ]]; then
    install_mongodb
fi

if [[ "$INCLUDE_TAPCLI" == "true" ]]; then
    install_tapcli
fi

if [[ "$INCLUDE_JDK" == "true" ]]; then
    install_jdk
fi

if [[ "$INCLUDE_LICENSE" == "true" ]]; then
    install_lisence
fi

tar -czf $ARTIFACT_PATH -C . $TARGET_DIR/

if [[ "$UPLOAD_TO_OSS" != "false" ]]; then
    install_ossutil
    upload_to_oss
fi
