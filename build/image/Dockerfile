FROM harbor.internal.tapdata.io/tapdata/tldp/runtime-ee:0.5

ENV JAVA_TOOL_OPTIONS -Dfile.encoding=UTF8
COPY tapdata /tapdata/apps/tapdata
COPY etc/ /tapdata/apps/etc
COPY connectors/dist.tar.gz /tapdata/apps/connectors/dist.tar.gz
COPY lib/ /tapdata/apps/lib
COPY components/ /tapdata/apps/components
COPY docker-entrypoint.sh /tapdata/docker-entrypoint.sh
COPY *.config /tapdata/apps/.config
COPY *.version /tapdata/apps/.version
COPY application.yml /tapdata/apps/application.yml

RUN wget 'http://192.168.1.184:5244/d/tools/java-17.tar.gz?sign=tKZO5QQTne90dRexZUoio6WNygJ_di_CScTa3wFuInY=:0' -O java-17.tar.gz && \
    tar -xzf java-17.tar.gz -C /usr/java && \
    rm -f java-17.tar.gz && \
    update-alternatives --install "/usr/bin/java" "java" "/usr/java/jdk-17.0.12/bin/java" 1 && \
    update-alternatives --install "/usr/bin/javac" "javac" "/usr/java/jdk-17.0.12/bin/javac" 1 && \
    update-alternatives --install "/usr/bin/jar" "jar" "/usr/java/jdk-17.0.12/bin/jar" 1 && \
    update-alternatives --install "/usr/bin/java" "java" "/usr/java/jdk1.8.0_311/bin/java" 1 && \
    update-alternatives --install "/usr/bin/javac" "javac" "/usr/java/jdk1.8.0_311/bin/javac" 1 && \
    update-alternatives --install "/usr/bin/jar" "jar" "/usr/java/jdk1.8.0_311/bin/jar" 1 && \
    wget 'http://192.168.1.184:5244/d/tools/java-11.tar.gz?sign=HuOuaQsPU_iyMSz9ctcPRscFPBbdcSENIGkJEPlt1dg=:0' -O java-11.tar.gz && \
    tar -xzf java-11.tar.gz -C /usr/java && \
    rm -f java-11.tar.gz && \
    update-alternatives --install "/usr/bin/java" "java" "/usr/java/jdk-11.0.25/bin/java" 1 && \
    update-alternatives --install "/usr/bin/javac" "javac" "/usr/java/jdk-11.0.25/bin/javac" 1 && \
    update-alternatives --install "/usr/bin/jar" "jar" "/usr/java/jdk-11.0.25/bin/jar" 1

RUN sed -i 's/http:\/\/archive.ubuntu.com\/ubuntu\//http:\/\/mirrors.aliyun.com\/ubuntu\//g' /etc/apt/sources.list && \
    sed -i 's/http:\/\/security.ubuntu.com\/ubuntu\//http:\/\/mirrors.aliyun.com\/ubuntu\//g' /etc/apt/sources.list && \
    apt-get clean && \
    # 清理所有可能存在的源配置
    rm -rf /etc/apt/sources.list.d/* && \
    rm -rf /etc/apt/trusted.gpg.d/* && \
    rm -rf /etc/apt/keyrings/* && \
    rm -rf /usr/share/keyrings/* && \
    # 重新获取 Ubuntu 密钥
    curl -sSL "http://192.168.1.184:5244/d/tools/f6ecb3762474eda9d21b7022871920d1991bc93c.asc?sign=JEgfYGuqtFWKMCK0WTiT5HbxLYvqcSwPeniDS4XRQPo=:0" | apt-key add - && \
    curl -sSL "http://192.168.1.184:5244/d/tools/790bc7277767219c42c86f933b4fe6acc0b21f32.asc?sign=AE1Ri798zgLlUjHqIqjYL9VymUICWemETDfn5iGm4lQ=:0" | apt-key add - && \
    apt-get update && \
    apt-get install -y ca-certificates gnupg && \
    # 安装必要工具
    apt-get install -y rsync && \
    echo 'Gotapd8!' > /tmp/rsync.passwd && chmod 0600 /tmp/rsync.passwd && \
    rsync -vzrt --password-file=/tmp/rsync.passwd rsync://root@192.168.1.184:873/data/enterprise-artifact/tools/async-profiler-3.0-linux-x64.tar.gz /tapdata/apps/async-profiler.tar.gz && \
    tar -xzf /tapdata/apps/async-profiler.tar.gz -C /tapdata/apps/ && \
    rm -f /tapdata/apps/async-profiler.tar.gz && \
    mv /tapdata/apps/async-profiler-* /tapdata/apps/async-profiler && \
    rm -f /tmp/rsync.passwd

RUN chmod +x /tapdata/docker-entrypoint.sh && mkdir -p /tapdata/data/db/

ENTRYPOINT ["bash", "-c", "/tapdata/docker-entrypoint.sh"]