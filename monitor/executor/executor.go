package executor

import (
	"bytes"
	"context"
	"fmt"
	"os/exec"
	"strings"
	"time"
)

// DefaultCommandTimeout specifies the default timeout for external commands.
const DefaultCommandTimeout = 15 * time.Second

// RunCommand executes an external command with a timeout and returns its stdout, stderr, and any execution error.
// It trims leading/trailing whitespace from the output.
func RunCommand(command string, args ...string) (string, string, error) {
	// Create a context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), DefaultCommandTimeout)
	defer cancel() // Ensure resources are cleaned up

	// Use CommandContext to associate the command with the cancellable context
	cmd := exec.CommandContext(ctx, command, args...)

	var stdoutBuf, stderrBuf bytes.Buffer
	cmd.Stdout = &stdoutBuf
	cmd.Stderr = &stderrBuf

	err := cmd.Run() // This will now respect the context timeout

	stdout := strings.TrimSpace(stdoutBuf.String())
	stderr := strings.TrimSpace(stderrBuf.String())

	if err != nil {
		// Check if the error was due to the context deadline exceeding
		if ctx.Err() == context.DeadlineExceeded {
			return stdout, stderr, fmt.Errorf("command '%s %s' timed out after %v; stderr: %s", command, strings.Join(args, " "), DefaultCommandTimeout, stderr)
		}
		// Include stderr in the error message if it's not empty for other errors
		if stderr != "" {
			return stdout, stderr, fmt.Errorf("command '%s %s' failed: %w; stderr: %s", command, strings.Join(args, " "), err, stderr)
		} else {
			return stdout, stderr, fmt.Errorf("command '%s %s' failed: %w", command, strings.Join(args, " "), err)
		}
	}

	return stdout, stderr, nil
}
 