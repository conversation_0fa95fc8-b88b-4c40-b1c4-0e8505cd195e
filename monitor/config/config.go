package config

import (
	"flag"
	"fmt"
	"os"
	"regexp"
	"strings"
)

// Constants for default values
const (
	// General
	DefaultLogfile     = "/var/log/infra_monitor.log"
	DefaultProjectCode = "SC3"

	// MongoDB Log Monitoring
	DefaultMongoLog  = "/var/log/mongodb/mongod.log"
	DefaultStateFile = "/var/tmp/mongo_last_check.state"
	DefaultLogFormat = "text"

	// Heap Utilization Monitoring
	DefaultHeapThreshold = 85
	DefaultJDKBinPath    = ""

	// CPU Utilization Monitoring
	DefaultCPUStateDir    = "/var/tmp/monitor_cpu"
	DefaultCPUMinorThresh = 70.0
	DefaultCPUMajorThresh = 90.0

	// MongoDB Health Monitoring
	DefaultMongoHost          = "127.0.0.1"
	DefaultMongoPort          = 27017
	DefaultMongoAuthDB        = "admin"
	DefaultQueuedOpsThreshold = 100
	DefaultLockPctMinorThresh = 30.0
	DefaultLockPctMajorThresh = 60.0
	DefaultReplLagMinorThresh = 60
	DefaultReplLagMajorThresh = 300

	// MongoDB Memory Monitoring
	DefaultMemStateDir       = "/var/tmp/monitor_mem"
	DefaultMemMinorThreshold = 70.0 // Percentage
	DefaultMemMajorThreshold = 90.0 // Percentage

	// Task Balance Monitoring
	DefaultTaskBalanceHostPort    = "" // Must be provided by user
	DefaultTaskBalanceAccessToken = "" // Must be provided by user
	DefaultTaskBalanceThresholdN  = 2
)

// Default process patterns
var DefaultHeapProcessPatterns = []string{"java.*tm.jar", "java.*tapdata-agent.jar"}
var DefaultCPUProcessPatterns = []string{"java.*tm.jar", "java.*tapdata-agent.jar"}

// --- Custom Flag Types ---

// StringSliceFlag is a custom flag type for repeated string flags
type StringSliceFlag []string

func (s *StringSliceFlag) String() string {
	return strings.Join(*s, ",")
}

func (s *StringSliceFlag) Set(value string) error {
	// Allow comma-separated values as well
	parts := strings.Split(value, ",")
	for _, part := range parts {
		trimmed := strings.TrimSpace(part)
		if trimmed != "" {
			*s = append(*s, trimmed)
		}
	}
	return nil
}

// MonitorTypeFlag is a custom flag type for -monitor-types
type MonitorTypeFlag map[string]bool

var validMonitorTypes = map[string]bool{"log": true, "heap": true, "cpu": true, "mongohealth": true, "mongomemory": true, "taskbalance": true}

func (m MonitorTypeFlag) String() string {
	var enabled []string
	for k, v := range m {
		if v {
			enabled = append(enabled, k)
		}
	}
	// Return default string if empty after parsing, handled in ParseFlags
	return strings.Join(enabled, ",")
}

func (m MonitorTypeFlag) Set(value string) error {
	parts := strings.Split(value, ",")
	for _, part := range parts {
		trimmed := strings.ToLower(strings.TrimSpace(part))
		if trimmed == "" {
			continue
		}
		if _, ok := validMonitorTypes[trimmed]; !ok {
			validTypesStr := "log, heap, cpu, mongohealth, mongomemory, taskbalance"
			return fmt.Errorf("invalid monitor type: %s. Valid types are %s", trimmed, validTypesStr)
		}
		m[trimmed] = true // Mark the type as enabled
	}
	return nil
}

// SeverityFlag is a custom flag type to handle multiple -S arguments
type SeverityFlag []string

func (s *SeverityFlag) String() string {
	return strings.Join(*s, ",")
}

func (s *SeverityFlag) Set(value string) error {
	normalized := strings.ToUpper(value[:1])
	switch normalized {
	case "F", "E", "W":
		// Avoid duplicates
		for _, existing := range *s {
			if existing == normalized {
				return nil // Already present
			}
		}
		*s = append(*s, normalized)
	default:
		return fmt.Errorf("invalid severity: %s. Use F, E, or W", value)
	}
	return nil
}

// --- Configuration Struct ---

// Config holds all the configuration parameters
type Config struct {
	// Enabled Monitor Types
	MonitorTypes map[string]bool

	// General Config
	LogfilePath string
	Hostname    string
	ProjectCode string

	// MongoDB Log Monitoring
	MongoLogPath  string
	StateFilePath string
	LogFormat     string
	Severities    []string

	// Heap Utilization Monitoring
	HeapProcessPatterns []string
	HeapThreshold       int
	JDKBinPath          string

	// CPU Utilization Monitoring
	CPUProcessPatterns []string
	CPUMinorThreshold  float64
	CPUMajorThreshold  float64
	CPUStateDir        string

	// MongoDB Health Monitoring
	MongoHost             string
	MongoPort             int
	MongoUser             string // Optional
	MongoPass             string // Optional
	MongoAuthDB           string
	QueuedOpsThreshold    int
	LockPctMinorThreshold float64
	LockPctMajorThreshold float64
	ReplLagMinorThreshold int // seconds
	ReplLagMajorThreshold int // seconds

	// MongoDB Memory Monitoring
	MemStateDir       string
	MemMinorThreshold float64 // Percentage
	MemMajorThreshold float64 // Percentage

	// Task Balance Monitoring
	TaskBalanceHostPort    string
	TaskBalanceAccessToken string
	TaskBalanceThresholdN  int
}

var defaultSeverities = []string{"F", "E", "W"}

// ParseFlags parses the command-line flags and returns a Config struct
func ParseFlags() *Config {
	cfg := &Config{
		MonitorTypes: make(map[string]bool), // Initialize the map
	}
	var monitorTypesFlag MonitorTypeFlag = cfg.MonitorTypes // Bind flag to the map
	var severities SeverityFlag
	var heapProcesses StringSliceFlag
	var cpuProcesses StringSliceFlag

	// General Flags
	flag.StringVar(&cfg.LogfilePath, "logfile", DefaultLogfile, "Path to the monitoring agent log file.")
	flag.StringVar(&cfg.ProjectCode, "project-code", DefaultProjectCode, "Project code to include in log messages.")
	// Default value description is handled in Usage message
	flag.Var(monitorTypesFlag, "monitor-types", `Comma-separated list of monitors to enable (log, heap, cpu, mongohealth, mongomemory, taskbalance). Default: "log"`)

	// MongoDB Log Flags
	flag.StringVar(&cfg.MongoLogPath, "mongo-log", DefaultMongoLog, "Path to the MongoDB log file (used only if 'log' monitor is enabled).")
	flag.StringVar(&cfg.StateFilePath, "state-file", DefaultStateFile, "Path to the state file storing last check info (used only if 'log' monitor is enabled).")
	flag.StringVar(&cfg.LogFormat, "format", DefaultLogFormat, "MongoDB log format (text or json, used only if 'log' monitor is enabled).")
	flag.Var(&severities, "severity", "MongoDB severity level to capture (F, E, or W). Can specify multiple times (used only if 'log' monitor is enabled).")
	flag.Var(&severities, "S", "Short alias for -severity.")

	// Heap Utilization Flags
	// Default value description is handled in Usage message
	flag.Var(&heapProcesses, "heap-process", "Java process name pattern (pgrep -f) to monitor heap usage. Can specify multiple times (used only if 'heap' monitor is enabled). Default: See README.")
	flag.IntVar(&cfg.HeapThreshold, "heap-threshold", DefaultHeapThreshold, "Heap usage threshold percentage (1-100) for Java processes (used only if 'heap' monitor is enabled).")
	flag.StringVar(&cfg.JDKBinPath, "jdk-path", DefaultJDKBinPath, "Optional path to the JDK bin directory (for jcmd/jstat, used only if 'heap' monitor is enabled).")

	// CPU Utilization Flags
	// Default value description is handled in Usage message
	flag.Var(&cpuProcesses, "process", "Process name pattern (pgrep -f) to monitor aggregated CPU usage. Can specify multiple times (used only if 'cpu' monitor is enabled). Default: See README.")
	flag.Var(&cpuProcesses, "p", "Short alias for -process.")
	flag.Float64Var(&cfg.CPUMinorThreshold, "minor-threshold", DefaultCPUMinorThresh, "Minor aggregated CPU usage threshold percentage (0.0-100.0, used only if 'cpu' monitor is enabled).")
	flag.Float64Var(&cfg.CPUMinorThreshold, "m", DefaultCPUMinorThresh, "Short alias for -minor-threshold.")
	flag.Float64Var(&cfg.CPUMajorThreshold, "major-threshold", DefaultCPUMajorThresh, "Major aggregated CPU usage threshold percentage (0.0-100.0, used only if 'cpu' monitor is enabled).")
	flag.Float64Var(&cfg.CPUMajorThreshold, "M", DefaultCPUMajorThresh, "Short alias for -major-threshold.")
	flag.StringVar(&cfg.CPUStateDir, "state-dir", DefaultCPUStateDir, "Directory to store high CPU state flags (used only if 'cpu' monitor is enabled).")
	flag.StringVar(&cfg.CPUStateDir, "s", DefaultCPUStateDir, "Short alias for -state-dir.")

	// MongoDB Health Flags
	flag.StringVar(&cfg.MongoHost, "mongo-host", DefaultMongoHost, "MongoDB host for health/memory checks (used only if 'mongohealth' or 'mongomemory' monitors are enabled).")
	flag.IntVar(&cfg.MongoPort, "mongo-port", DefaultMongoPort, "MongoDB port for health/memory checks (used only if 'mongohealth' or 'mongomemory' monitors are enabled).")
	flag.StringVar(&cfg.MongoUser, "mongo-user", "", "MongoDB username for health/memory checks (optional).")
	flag.StringVar(&cfg.MongoPass, "mongo-pass", "", "MongoDB password for health/memory checks (optional).")
	flag.StringVar(&cfg.MongoAuthDB, "mongo-authdb", DefaultMongoAuthDB, "MongoDB authentication database (used only if user/pass provided).")
	flag.IntVar(&cfg.QueuedOpsThreshold, "queued-op-threshold", DefaultQueuedOpsThreshold, "Threshold for queued operations (serverStatus) (used only if 'mongohealth' monitor is enabled).")
	flag.Float64Var(&cfg.LockPctMinorThreshold, "lock-pct-threshold-minor", DefaultLockPctMinorThresh, "Minor threshold for lock percentage (serverStatus) (used only if 'mongohealth' monitor is enabled).")
	flag.Float64Var(&cfg.LockPctMajorThreshold, "lock-pct-threshold-major", DefaultLockPctMajorThresh, "Major threshold for lock percentage (serverStatus) (used only if 'mongohealth' monitor is enabled).")
	flag.IntVar(&cfg.ReplLagMinorThreshold, "repl-lag-threshold-minor", DefaultReplLagMinorThresh, "Minor threshold for replication lag in seconds (rs.status) (used only if 'mongohealth' monitor is enabled).")
	flag.IntVar(&cfg.ReplLagMajorThreshold, "repl-lag-threshold-major", DefaultReplLagMajorThresh, "Major threshold for replication lag in seconds (rs.status) (used only if 'mongohealth' monitor is enabled).")

	// MongoDB Memory Flags
	flag.Float64Var(&cfg.MemMinorThreshold, "mem-threshold-minor", DefaultMemMinorThreshold, "Minor threshold for MongoDB resident memory usage percentage (used only if 'mongomemory' monitor is enabled).")
	flag.Float64Var(&cfg.MemMajorThreshold, "mem-threshold-major", DefaultMemMajorThreshold, "Major threshold for MongoDB resident memory usage percentage (used only if 'mongomemory' monitor is enabled).")
	flag.StringVar(&cfg.MemStateDir, "mem-state-dir", DefaultMemStateDir, "Directory to store high memory state flags (used only if 'mongomemory' monitor is enabled).")

	// Task Balance Monitoring Flags
	flag.StringVar(&cfg.TaskBalanceHostPort, "taskbalance-hostport", DefaultTaskBalanceHostPort, "Host and port (e.g., *************:3030) for the task balance API (used only if 'taskbalance' monitor is enabled). Required.")
	flag.StringVar(&cfg.TaskBalanceAccessToken, "taskbalance-access-token", DefaultTaskBalanceAccessToken, "Access token for the task balance API (used only if 'taskbalance' monitor is enabled). Required.")
	flag.IntVar(&cfg.TaskBalanceThresholdN, "taskbalance-threshold-n", DefaultTaskBalanceThresholdN, "Threshold N for task balance (abs(engine_tasks - avg_tasks) > N triggers alert) (used only if 'taskbalance' monitor is enabled).")

	// Custom usage message
	flag.Usage = func() {
		fmt.Fprintf(flag.CommandLine.Output(), "Usage of %s:\n", os.Args[0])
		fmt.Fprintf(flag.CommandLine.Output(), "\nGeneral Options:\n")
		flag.VisitAll(func(f *flag.Flag) {
			if f.Name == "logfile" || f.Name == "monitor-types" || f.Name == "project-code" {
				// Manually handle default display for monitor-types
				defaultValue := f.DefValue
				if f.Name == "monitor-types" {
					defaultValue = "log"
				}
				fmt.Fprintf(flag.CommandLine.Output(), "  -%-25s %s (Default: %v)\n", f.Name, f.Usage, defaultValue)
			}
		})
		fmt.Fprintf(flag.CommandLine.Output(), "\nLog Monitoring Options (only active if 'log' included in -monitor-types):\n")
		flag.VisitAll(func(f *flag.Flag) {
			if f.Name == "mongo-log" || f.Name == "state-file" || f.Name == "format" || f.Name == "severity" || f.Name == "S" {
				fmt.Fprintf(flag.CommandLine.Output(), "  -%-25s %s (Default: %v)\n", f.Name, f.Usage, f.DefValue)
			}
		})
		fmt.Fprintf(flag.CommandLine.Output(), "\nHeap Monitoring Options (only active if 'heap' included in -monitor-types):\n")
		flag.VisitAll(func(f *flag.Flag) {
			if f.Name == "heap-process" || f.Name == "heap-threshold" || f.Name == "jdk-path" {
				// Manually handle default display for heap-process
				defaultValue := f.DefValue
				if f.Name == "heap-process" {
					defaultValue = fmt.Sprintf(`"%s"`, strings.Join(DefaultHeapProcessPatterns, `", "`))
				}
				fmt.Fprintf(flag.CommandLine.Output(), "  -%-25s %s (Default: %v)\n", f.Name, f.Usage, defaultValue)
			}
		})
		fmt.Fprintf(flag.CommandLine.Output(), "\nCPU Monitoring Options (only active if 'cpu' included in -monitor-types):\n")
		flag.VisitAll(func(f *flag.Flag) {
			if f.Name == "process" || f.Name == "p" || f.Name == "minor-threshold" || f.Name == "m" || f.Name == "major-threshold" || f.Name == "M" || f.Name == "state-dir" || f.Name == "s" {
				// Manually handle default display for process/p and thresholds
				defaultValue := f.DefValue
				if f.Name == "process" || f.Name == "p" {
					defaultValue = fmt.Sprintf(`"%s"`, strings.Join(DefaultCPUProcessPatterns, `", "`))
				} else if f.Name == "minor-threshold" || f.Name == "m" {
					defaultValue = fmt.Sprintf("%.1f", DefaultCPUMinorThresh)
				} else if f.Name == "major-threshold" || f.Name == "M" {
					defaultValue = fmt.Sprintf("%.1f", DefaultCPUMajorThresh)
				}
				fmt.Fprintf(flag.CommandLine.Output(), "  -%-25s %s (Default: %v)\n", f.Name, f.Usage, defaultValue)
			}
		})
		fmt.Fprintf(flag.CommandLine.Output(), "\nMongo Health Options (only active if 'mongohealth' included in -monitor-types):\n")
		flag.VisitAll(func(f *flag.Flag) {
			if strings.HasPrefix(f.Name, "mongo-") || strings.HasPrefix(f.Name, "queued-") || strings.HasPrefix(f.Name, "lock-pct-") || strings.HasPrefix(f.Name, "repl-lag-") {
				fmt.Fprintf(flag.CommandLine.Output(), "  -%-25s %s (Default: %v)\n", f.Name, f.Usage, f.DefValue)
			}
		})
		fmt.Fprintf(flag.CommandLine.Output(), "\nMongo Memory Options (only active if 'mongomemory' included in -monitor-types):\n")
		flag.VisitAll(func(f *flag.Flag) {
			if strings.HasPrefix(f.Name, "mem-") {
				defaultValue := f.DefValue
				if f.Name == "mem-threshold-minor" {
					defaultValue = fmt.Sprintf("%.1f", DefaultMemMinorThreshold)
				} else if f.Name == "mem-threshold-major" {
					defaultValue = fmt.Sprintf("%.1f", DefaultMemMajorThreshold)
				}
				fmt.Fprintf(flag.CommandLine.Output(), "  -%-25s %s (Default: %v)\n", f.Name, f.Usage, defaultValue)
			}
		})
		fmt.Fprintf(flag.CommandLine.Output(), "\nTask Balance Monitoring Options (only active if 'taskbalance' included in -monitor-types):\n")
		flag.VisitAll(func(f *flag.Flag) {
			if isTaskBalanceFlag(f.Name) {
				defaultValue := f.DefValue
				if f.Name == "taskbalance-hostport" && defaultValue == "" {
					defaultValue = "Required"
				}
				if f.Name == "taskbalance-access-token" && defaultValue == "" {
					defaultValue = "Required"
				}
				fmt.Fprintf(flag.CommandLine.Output(), "  -%-25s %s (Default: %v)\n", f.Name, f.Usage, defaultValue)
			}
		})
		fmt.Fprintf(flag.CommandLine.Output(), "\nExample: %s -monitor-types log,cpu -p myapp -M 90 -logfile /var/log/app_monitor.log\n", os.Args[0])
	}

	flag.Parse()

	// Set default monitor type if none provided by user
	if len(cfg.MonitorTypes) == 0 {
		cfg.MonitorTypes["log"] = true
	}

	// --- Assign other parsed flags to config --- //

	// Log Monitor specific
	if len(severities) == 0 {
		cfg.Severities = defaultSeverities
	} else {
		cfg.Severities = severities
	}

	// Heap Monitor specific
	if len(heapProcesses) > 0 {
		cfg.HeapProcessPatterns = heapProcesses
	} else if cfg.MonitorTypes["heap"] {
		// Apply default only if heap monitoring is enabled and user didn't provide any
		cfg.HeapProcessPatterns = DefaultHeapProcessPatterns
	}

	// CPU Monitor specific
	if len(cpuProcesses) > 0 {
		cfg.CPUProcessPatterns = cpuProcesses
	} else if cfg.MonitorTypes["cpu"] {
		// Apply default only if cpu monitoring is enabled and user didn't provide any
		cfg.CPUProcessPatterns = DefaultCPUProcessPatterns
	}

	// Task Balance Monitoring specific

	// --- Validations (only if corresponding monitor is enabled) --- //

	if cfg.MonitorTypes["heap"] {
		if cfg.HeapThreshold < 1 || cfg.HeapThreshold > 100 {
			fmt.Fprintf(os.Stderr, "Error: Invalid heap threshold %d. Must be between 1 and 100.\n", cfg.HeapThreshold)
			os.Exit(1)
		}
		// Validation: If heap monitoring is on, patterns list must not be empty (either user-provided or default)
		if len(cfg.HeapProcessPatterns) == 0 {
			fmt.Fprintf(os.Stderr, "Error: Heap monitoring enabled but no -heap-process patterns were provided or defaulted.\n") // Should not happen with default logic
			os.Exit(1)
		}
	}

	if cfg.MonitorTypes["cpu"] {
		// Validate float thresholds: >= 0.0 and <= 100.0
		if cfg.CPUMinorThreshold < 0.0 || cfg.CPUMinorThreshold > 100.0 {
			fmt.Fprintf(os.Stderr, "Error: Invalid minor CPU threshold %.2f. Must be between 0.0 and 100.0.\n", cfg.CPUMinorThreshold)
			os.Exit(1)
		}
		if cfg.CPUMajorThreshold < 0.0 || cfg.CPUMajorThreshold > 100.0 {
			fmt.Fprintf(os.Stderr, "Error: Invalid major CPU threshold %.2f. Must be between 0.0 and 100.0.\n", cfg.CPUMajorThreshold)
			os.Exit(1)
		}
		if cfg.CPUMinorThreshold > cfg.CPUMajorThreshold {
			fmt.Fprintf(os.Stderr, "Error: Minor CPU threshold (%.2f) cannot be greater than Major CPU threshold (%.2f).\n", cfg.CPUMinorThreshold, cfg.CPUMajorThreshold)
			os.Exit(1)
		}
		if err := os.MkdirAll(cfg.CPUStateDir, 0755); err != nil {
			fmt.Fprintf(os.Stderr, "Error: Cannot create CPU state directory '%s': %v\n", cfg.CPUStateDir, err)
			os.Exit(1)
		}
		tempFile := cfg.CPUStateDir + "/.check_writable"
		if f, err := os.Create(tempFile); err != nil {
			fmt.Fprintf(os.Stderr, "Error: CPU state directory '%s' is not writable: %v\n", cfg.CPUStateDir, err)
			os.Exit(1)
		} else {
			f.Close()
			os.Remove(tempFile)
		}
		// Validation: If cpu monitoring is on, patterns list must not be empty (either user-provided or default)
		if len(cfg.CPUProcessPatterns) == 0 {
			fmt.Fprintf(os.Stderr, "Error: CPU monitoring enabled but no -process/-p patterns were provided or defaulted.\n") // Should not happen with default logic
			os.Exit(1)
		}
	}

	if cfg.MonitorTypes["mongohealth"] {
		// Validate lock thresholds
		if cfg.LockPctMinorThreshold < 0.0 || cfg.LockPctMinorThreshold > 100.0 {
			fmt.Fprintf(os.Stderr, "Error: Invalid minor lock percentage threshold %.2f. Must be between 0.0 and 100.0.\n", cfg.LockPctMinorThreshold)
			os.Exit(1)
		}
		if cfg.LockPctMajorThreshold < 0.0 || cfg.LockPctMajorThreshold > 100.0 {
			fmt.Fprintf(os.Stderr, "Error: Invalid major lock percentage threshold %.2f. Must be between 0.0 and 100.0.\n", cfg.LockPctMajorThreshold)
			os.Exit(1)
		}
		if cfg.LockPctMinorThreshold > cfg.LockPctMajorThreshold {
			fmt.Fprintf(os.Stderr, "Error: Minor lock percentage threshold (%.2f) cannot be greater than Major lock percentage threshold (%.2f).\n", cfg.LockPctMinorThreshold, cfg.LockPctMajorThreshold)
			os.Exit(1)
		}
		// Validate repl lag thresholds
		if cfg.ReplLagMinorThreshold < 0 {
			fmt.Fprintf(os.Stderr, "Error: Invalid minor replication lag threshold %d. Cannot be negative.\n", cfg.ReplLagMinorThreshold)
			os.Exit(1)
		}
		if cfg.ReplLagMajorThreshold < 0 {
			fmt.Fprintf(os.Stderr, "Error: Invalid major replication lag threshold %d. Cannot be negative.\n", cfg.ReplLagMajorThreshold)
			os.Exit(1)
		}
		if cfg.ReplLagMinorThreshold > cfg.ReplLagMajorThreshold {
			fmt.Fprintf(os.Stderr, "Error: Minor replication lag threshold (%d) cannot be greater than Major replication lag threshold (%d).\n", cfg.ReplLagMinorThreshold, cfg.ReplLagMajorThreshold)
			os.Exit(1)
		}
		// Validate queued ops threshold
		if cfg.QueuedOpsThreshold < 0 {
			fmt.Fprintf(os.Stderr, "Error: Invalid queued operations threshold %d. Cannot be negative.\n", cfg.QueuedOpsThreshold)
			os.Exit(1)
		}
		// Validate auth config consistency (only if mongohealth or mongomemory is enabled)
		if (cfg.MongoUser != "" && cfg.MongoPass == "") || (cfg.MongoUser == "" && cfg.MongoPass != "") {
			fmt.Fprintf(os.Stderr, "Error: MongoDB username and password must both be provided or both be omitted for health/memory checks.\n")
			os.Exit(1)
		}
	}

	if cfg.MonitorTypes["mongomemory"] {
		// Validate memory thresholds
		if cfg.MemMinorThreshold < 0.0 || cfg.MemMinorThreshold > 100.0 {
			fmt.Fprintf(os.Stderr, "Error: Invalid minor memory threshold %.2f. Must be between 0.0 and 100.0.\n", cfg.MemMinorThreshold)
			os.Exit(1)
		}
		if cfg.MemMajorThreshold < 0.0 || cfg.MemMajorThreshold > 100.0 {
			fmt.Fprintf(os.Stderr, "Error: Invalid major memory threshold %.2f. Must be between 0.0 and 100.0.\n", cfg.MemMajorThreshold)
			os.Exit(1)
		}
		if cfg.MemMinorThreshold > cfg.MemMajorThreshold {
			fmt.Fprintf(os.Stderr, "Error: Minor memory threshold (%.2f) cannot be greater than Major memory threshold (%.2f).\n", cfg.MemMinorThreshold, cfg.MemMajorThreshold)
			os.Exit(1)
		}
		// Validate and check state dir writability
		if err := os.MkdirAll(cfg.MemStateDir, 0755); err != nil {
			fmt.Fprintf(os.Stderr, "Error: Cannot create Memory state directory '%s': %v\n", cfg.MemStateDir, err)
			os.Exit(1)
		}
		tempFile := cfg.MemStateDir + "/.check_writable"
		if f, err := os.Create(tempFile); err != nil {
			fmt.Fprintf(os.Stderr, "Error: Memory state directory '%s' is not writable: %v\n", cfg.MemStateDir, err)
			os.Exit(1)
		} else {
			f.Close()
			os.Remove(tempFile)
		}
		// Validate auth consistency if needed for this check specifically?
		if (cfg.MongoUser != "" && cfg.MongoPass == "") || (cfg.MongoUser == "" && cfg.MongoPass != "") {
			fmt.Fprintf(os.Stderr, "Error: MongoDB username and password must both be provided or both be omitted for memory checks.\n")
			os.Exit(1)
		}
	}

	if cfg.MonitorTypes["taskbalance"] {
		if cfg.TaskBalanceHostPort == "" {
			fmt.Fprintln(os.Stderr, "Error: -taskbalance-hostport is required when 'taskbalance' monitor is enabled.")
			flag.Usage()
			os.Exit(2)
		}
		if cfg.TaskBalanceAccessToken == "" {
			fmt.Fprintln(os.Stderr, "Error: -taskbalance-access-token is required when 'taskbalance' monitor is enabled.")
			flag.Usage()
			os.Exit(2)
		}
	}

	// Get hostname
	var err error
	cfg.Hostname, err = os.Hostname()
	if err != nil {
		cfg.Hostname = "unknown"
		fmt.Fprintf(os.Stderr, "WARN: Could not get hostname: %v\n", err)
	}

	return cfg
}

// SanitizePatternForFilename replaces characters unsafe for filenames with underscores.
func SanitizePatternForFilename(pattern string) string {
	// Replace common problematic characters like /, \, :, *, ?, ", <, >, |
	re := regexp.MustCompile(`[/\:*?"<>|\s]`) // Include whitespace just in case
	return re.ReplaceAllString(pattern, "_")
}

func isMongoHealthFlag(name string) bool {
	return strings.HasPrefix(name, "mongo-") || strings.HasPrefix(name, "queued-") || strings.HasPrefix(name, "lock-pct-") || strings.HasPrefix(name, "repl-lag-")
}

func isMongoMemoryFlag(name string) bool {
	return name == "mem-threshold-minor" || name == "mem-threshold-major" || name == "mem-state-dir" ||
		name == "mongo-host" || name == "mongo-port" || name == "mongo-user" || name == "mongo-pass" || name == "mongo-authdb"
}

func isTaskBalanceFlag(name string) bool {
	return name == "taskbalance-hostport" || name == "taskbalance-access-token" || name == "taskbalance-threshold-n"
}
