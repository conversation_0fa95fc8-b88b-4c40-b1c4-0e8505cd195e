#!/bin/bash

# ====== CONFIGURATION ======
PROCESSES=("nginx" "mysqld" "sshd")
PROJECT_CODE="SC3"
LOGFILE="/var/log/infra_monitor.log"
HOSTNAME=$(hostname)

# ====== FUNCTIONS ======

log_message() {
    local severity="$1"
    local msg="$2"

    # Timestamp format: YYMMDD HHMMSS
    local timestamp
    timestamp=$(date '+%y%m%d %H%M%S')

    # Truncate message if too long
    local clean_msg="${msg:0:2000}"

    echo "$timestamp [$severity] $clean_msg ($PROJECT_CODE)" >> "$LOGFILE"
}

restart_process() {
    local pname="$1"

    # Try systemctl first, fallback to service
    if systemctl list-units --type=service --all | grep -q -w "${pname}.service"; then
        systemctl restart "$pname" && \
            log_message "MINOR" "$HOSTNAME ${pname} service RESTARTED SUCCESSFULLY" || \
            log_message "MAJOR" "$HOSTNAME ${pname} service RESTART FAILED"
    elif command -v service &>/dev/null; then
        service "$pname" restart && \
            log_message "MINOR" "$HOSTNAME ${pname} service RESTARTED SUCCESSFULLY" || \
            log_message "MAJOR" "$HOSTNAME ${pname} service RESTART FAILED"
    else
        log_message "MAJOR" "$HOSTNAME ${pname} restart mechanism NOT FOUND"
    fi
}

check_process() {
    local pname="$1"
    local pids
    pids=$(pgrep -x "$pname")

    if [ -z "$pids" ]; then
        log_message "MAJOR" "$HOSTNAME ${pname} service DOWN"
        restart_process "$pname"
        return
    fi

    for pid in $pids; do
        local state
        state=$(ps -o stat= -p "$pid" | awk '{print $1}')

        if [[ "$state" =~ [Z] ]]; then
            log_message "MAJOR" "$HOSTNAME ${pname} (PID $pid) is ZOMBIE — attempting to kill and restart"
            kill -9 "$pid" && log_message "MINOR" "$HOSTNAME ${pname} zombie PID $pid killed" || log_message "MAJOR" "$HOSTNAME FAILED to kill zombie PID $pid"
            restart_process "$pname"
        else
            log_message "MINOR" "$HOSTNAME ${pname} service UP"
        fi
    done
}

# ====== MAIN LOOP ======
for proc in "${PROCESSES[@]}"; do
    check_process "$proc"
done
