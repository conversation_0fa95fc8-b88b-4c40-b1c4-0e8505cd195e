# MongoDB 日志及系统资源监控器 (Go 版本)

本 Go 应用程序用于监控 MongoDB 日志文件和指定进程的系统资源使用情况（CPU、堆内存），以及 MongoDB 自身的健康状况，并将异常情况报告到中央监控日志文件中。用户可以通过命令行标志精确控制启用哪些监控项。

## 功能特性

可以通过 `-monitor-types` 标志启用以下一项或多项监控功能（逗号分隔）：`log`, `heap`, `cpu`, `mongohealth`, `taskbalance`。

**日志监控 (`log`):**

- 监控指定的 MongoDB 日志文件 (`-mongo-log`)。
- 支持传统的文本格式和 JSON 日志格式 (`-format`)。
- 基于文件 inode 变化检测日志轮转。
- 在状态文件 (`-state-file`) 中持久化最后处理的时间戳和 inode，以避免重启后重复处理。
- 根据严重级别过滤日志条目 (`-severity` 或 `-S`，可多次指定，默认为 F, E, W)。
- 将 MongoDB 严重级别映射为监控级别 (F, E -> MAJOR; W -> MINOR)。

**堆内存监控 (`heap`):**

- 通过 `pgrep -f` 查找匹配特定模式 (`-heap-process`) 的 Java 进程。
- 使用 `jstat -gc` 获取指定 Java 进程的**整体堆使用率** (Eden + Old)。
- 当使用率超过设定的阈值 (`-heap-threshold`) 时，记录 MAJOR 级别日志并使用 `kill -9` 终止该进程。
- 可以通过 `-jdk-path` 指定 JDK 的 bin 目录路径，如果 `jstat` 或 `jcmd` 不在系统 PATH 中。

**CPU 使用率监控 (`cpu`):**

- 通过 `pgrep -f` 查找匹配特定模式 (`-process` 或 `-p`) 的所有进程。
- 使用 `ps -p <pid> -o %cpu=` 获取每个找到的进程的 CPU 使用率。
- 计算匹配同一模式的所有进程的 **聚合 CPU 使用率**。
- 使用状态文件 (`-state-dir` 下，文件名基于模式生成）来跟踪持续高 CPU 状态。**只有当 CPU 持续高于阈值时，才会记录日志**。
- 当聚合 CPU 使用率持续高于主要阈值 (`-major-threshold` 或 `-M`) 时，记录 MAJOR 级别日志。
- 当聚合 CPU 使用率持续高于次要阈值 (`-minor-threshold` 或 `-m`) 时，记录 MINOR 级别日志。
- 如果进程未运行，记录 MAJOR 级别日志。

**MongoDB 健康监控 (`mongohealth`):**

- 连接到指定的 MongoDB 实例 (`-mongo-host`, `-mongo-port`)，支持认证 (`-mongo-user`, `-mongo-pass`, `-mongo-authdb`)。
- 执行 `serverStatus` 命令检查：
    - 服务器是否响应。
    - 是否有非零的断言 (`asserts`)。
    - 检查队列中的操作数 (`globalLock.currentQueue.total`) 是否超过阈值 (`-queued-op-threshold`)。
    - 估算全局锁百分比（基于 `globalLock.totalTime` 和 `uptimeMillis`）并对照阈值 (`-lock-pct-threshold-minor`, `-lock-pct-threshold-major`) 告警。
- 如果是复制集成员，执行 `replSetGetStatus` 命令检查：
    - 节点的健康状态 (`myState`) 是否异常。
    - 如果是 Secondary 节点，计算与 Primary 的复制延迟（基于 `optime.ts`）并对照阈值 (`-repl-lag-threshold-minor`, `-repl-lag-threshold-major`) 告警。

**任务调度均衡监控 (`taskbalance`):**

- **目的**: 监控多个引擎间的任务调度均衡情况，当调度不均衡时触发告警。
- **工作原理**:
    - **动态引擎发现**: 程序首先调用 `/api/clusterStates` API (使用配置的 `-taskbalance-hostport` 和 `-taskbalance-access-token`) 来获取集群中所有节点的信息。它会自动筛选出其中 `engine.status` 为 "running" 的引擎，并提取它们的 `process_id` (用于后续查询) 和 `hostname` (用于日志显示)。
    - **获取任务数**: 使用上一步获取的 `process_id` 列表，程序接着调用 `/api/Workers/getProcessInfo` API 来获取每个活跃引擎当前运行的任务数 (`runningNum`)。
    - **计算与比较**: 计算所有被监控的活跃引擎当前运行任务总数的平均值。
    - 将每个引擎的运行任务数与平均值进行比较。
    - 如果某个引擎的运行任务数与平均值的差的绝对值大于设定的阈值 `N`，则触发告警。
- **API 依赖**:
    - 程序内部会自动构建到以下两个 API 端点的请求:
        - `http://<hostport>/api/clusterStates?access_token=<token>&index=1` (用于发现活跃引擎)
        - `http://<hostport>/api/Workers/getProcessInfo?access_token=<token>&process_id=["id1","id2",...]` (用于获取任务数)
    - 响应结构需要符合程序预期 (详见代码内定义的结构体)。
- **告警规则**:
    - 当 `|单个引擎运行任务数 - 总运行任务数平均值| > N` 时触发。
    - `N` 为可配置的阈值 (`-taskbalance-threshold-n`)。
- **告警内容示例**:
    - `引擎 engine-hostname-01 (ID: 06eefee4-...) 当前运行任务数为 10，比当前总运行任务数的平均值 8.00 大 2.00，任务调度出现不均衡，请及时关注`
    - `引擎 engine-hostname-02 (ID: another-id-...) 当前运行任务数为 6，比当前总运行任务数的平均值 8.00 小 2.00，任务调度出现不均衡，请及时关注`
- **配置项 (通过命令行标志)**:
    - `-monitor-types taskbalance` (启用此监控)
    - `-taskbalance-hostport "<IP_ADDRESS:PORT>"` (必需，例如: `"*************:3030"`。指定 API 服务器的主机和端口。)
    - `-taskbalance-access-token "<YOUR_ACCESS_TOKEN>"` (必需，用于 API 认证的访问令牌。)
    - `-taskbalance-threshold-n <整数>` (可选，告警阈值，默认为 `2`。)
- **前提条件**:
    - 运行监控程序的服务器需要能够通过网络访问配置的 API 服务器的 `hostport`。
    - 提供的 `access_token` 必须有权限访问 `/api/clusterStates` 和 `/api/Workers/getProcessInfo` 这两个 API 端点。

**通用:**

- 所有监控信息都将写入由 `-logfile` 指定的日志文件。
- 通过 `-project-code` 指定日志中附加的项目代码。
- 通过命令行标志进行配置，并提供合理的默认值。

## 前提条件

- 已安装 Go 语言环境（推荐 Go 1.16 或更高版本）。
- **项目依赖:** 需要运行 `go mod tidy` 来下载 `go.mongodb.org/mongo-driver` 等依赖。
- **通用系统命令:** `pgrep`, `ps`, `kill` 命令需要可用（通常已包含在标准 Linux/macOS 环境中）。
- **堆内存监控 (如果启用 `heap`):**
    - 目标服务器上需要安装 JDK，并且 `jstat`, `jcmd` 命令可用。
    - 运行此监控程序的操作系统用户需要有权限执行 `jstat`, `jcmd` 和 `kill` 相关 Java 进程。
- **CPU 监控 (如果启用 `cpu`):**
    - 运行此监控程序的操作系统用户需要有权限执行 `ps` 来查看目标进程的 CPU 使用率。
- **Mongo 健康监控 (如果启用 `mongohealth`):**
    - 程序运行的环境需要能够网络访问目标 MongoDB 实例。
    - 如果需要认证，提供的用户需要有执行 `serverStatus` 和 `replSetGetStatus` 的权限。
- **权限:** 需要对日志文件 (`-logfile`)、MongoDB 日志状态文件 (`-state-file`，如果启用 `log`) 和 CPU 状态目录 (`-state-dir`，如果启用 `cpu`) 具有读写权限。

## 构建

在终端中，导航到 `monitor` 目录并运行：

```bash
go mod tidy # 确保依赖已下载
go build .
# 或者构建 Linux 版本:
# GOOS=linux GOARCH=amd64 go build -o monitor-linux-amd64 .
```

这将在当前目录创建一个名为 `monitor` (或 `monitor-linux-amd64`) 的可执行文件。

## 使用方法

运行编译后的二进制文件，并使用 `-monitor-types` 指定要启用的监控类型（`log`, `heap`, `cpu`, `mongohealth`, `taskbalance`，逗号分隔）。**默认仅启用 `log` 监控。**

**示例：**

*   **默认行为：仅监控 MongoDB 日志（使用默认路径和设置）：**
    ```bash
    ./monitor
    # 等同于 ./monitor -monitor-types log
    ```

*   **仅执行 MongoDB 健康检查 (连接本地默认端口，无认证)：**
    ```bash
    ./monitor -monitor-types mongohealth
    ```

*   **执行 MongoDB 健康检查 (连接远程带认证的实例，调整复制延迟阈值)：**
    ```bash
    ./monitor -monitor-types mongohealth \
        -mongo-host db.example.com \
        -mongo-port 27018 \
        -mongo-user monitor_user \
        -mongo-pass "secret_password" \
        -mongo-authdb admin \
        -repl-lag-threshold-minor 120 \
        -repl-lag-threshold-major 600
    ```

*   **执行堆内存和 CPU 监控：**
    ```bash
    ./monitor -monitor-types heap,cpu \
        -heap-process my-app.jar \
        -p my-app.jar -m 75 -M 95
    ```

*   **执行所有监控类型：**
    ```bash
    ./monitor -monitor-types log,heap,cpu,mongohealth \
        -logfile /var/log/full_monitor.log \
        -project-code APP01
        # ... 其他特定类型的标志 ...
    ```

*   **查看帮助（包含所有选项）：**
    ```bash
    ./monitor -h
    ```

## 包结构

- `main.go`: 主应用程序入口点，协调配置、日志记录和根据 `-monitor-types` 调用各项检查流程。
- `config/`: 处理命令行标志解析和配置存储，包括 `-monitor-types`。
- `logger/`: 管理向输出文件和 stderr 写入格式化日志。
- `executor/`: 提供执行外部命令（如 pgrep, jstat, kill, ps）的通用函数。
- `mongohealth/`: (用于 `mongohealth`) 使用 Go Driver 执行 MongoDB 健康检查。
- `parser/`: (用于 `log`) 包含不同 MongoDB 日志格式 (JSON, text) 的解析器。
- `processor/`: (用于 `log`) 实现核心的 MongoDB 日志读取和处理逻辑。
- `state/`: (用于 `log`) 处理 MongoDB 日志监控的状态文件（inode, timestamp）的读写。
- `heap/`: (用于 `heap`) 实现 Java 进程查找、堆内存检查和进程终止逻辑。
- `cpu/`: (用于 `cpu`) 实现进程查找、聚合 CPU 检查、状态文件管理（用于持续告警）逻辑。 