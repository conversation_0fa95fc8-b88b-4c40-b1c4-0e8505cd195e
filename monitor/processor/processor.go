package processor

import (
	"bufio"
	"fmt"
	"monitor/config"
	"monitor/logger"
	"monitor/parser"
	"os"
	"time"
)

// Processor holds the components needed to process the log file.
type Processor struct {
	config    *config.Config
	startTime time.Time // Only process lines after this time
	parser    parser.Parser
}

// NewProcessor creates a new Processor instance.
func NewProcessor(cfg *config.Config, startTime time.Time) (*Processor, error) {
	var p parser.Parser
	var err error

	if cfg.LogFormat == "json" {
		p = parser.NewJsonParser()
	} else if cfg.LogFormat == "text" {
		p, err = parser.NewTextParser(cfg.Severities)
		if err != nil {
			return nil, fmt.Errorf("failed to create text parser: %w", err)
		}
	} else {
		return nil, fmt.Errorf("invalid log format specified: %s", cfg.LogFormat)
	}

	return &Processor{
		config:    cfg,
		startTime: startTime,
		parser:    p,
	}, nil
}

// Run starts the log processing loop.
func (proc *Processor) Run(mongoLog *os.File) error {
	scanner := bufio.NewScanner(mongoLog)
	processedCount := 0

	for scanner.Scan() {
		line := scanner.Text()
		if line == "" {
			continue
		}

		// Parse the line using the selected parser
		logEntry, err := proc.parser.Parse(line)
		if err != nil {
			// Silently skip lines that fail parsing (could log as DEBUG if needed)
			// logger.LogMonitor("DEBUG", fmt.Sprintf("Skipping unparseable line: %v | Line: %s", err, line))
			continue
		}

		// Check timestamp and severity
		if logEntry.Timestamp.After(proc.startTime) && proc.isSeverityMonitored(logEntry.SeverityCode) {
			monitorSev := proc.mapMongoSeverity(logEntry.SeverityCode)
			logger.LogMonitor(monitorSev, fmt.Sprintf("MongoDB log: %s", logEntry.RawLine))
			processedCount++
		}
	}

	if err := scanner.Err(); err != nil {
		// Log scanner errors separately
		return fmt.Errorf("error reading MongoDB log file: %w", err)
	}

	// Optional: Log summary
	// logger.LogMonitor("INFO", fmt.Sprintf("Processed %d new MongoDB log entries matching criteria.", processedCount))
	return nil
}

// Helper to check if severity is in the list (mirrors logger/main helper)
func (proc *Processor) isSeverityMonitored(code string) bool {
	for _, s := range proc.config.Severities {
		if code == s {
			return true
		}
	}
	return false
}

// Helper to map severity (mirrors logger/main helper)
func (proc *Processor) mapMongoSeverity(mongoSev string) string {
	switch mongoSev {
	case "F", "E":
		return "MAJOR"
	case "W":
		return "MINOR"
	default:
		return "INFO"
	}
}
