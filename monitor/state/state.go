package state

import (
	"fmt"
	"os"
	"strings"
	"time"
)

const (
	rfc3339NanoUTC = "2006-01-02T15:04:05.999999999Z" // Go's layout for RFC3339Nano UTC
)

// State represents the information stored in the state file.
type State struct {
	Inode     uint64
	Timestamp time.Time // Always store as UTC
}

// ReadState reads the state (inode, timestamp) from the specified file.
// If the file doesn't exist or is invalid, it returns a zero State and no error (or specific error).
func ReadState(filePath string) (State, error) {
	var state State // Zero value has zero inode and zero time

	data, err := os.ReadFile(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			return state, nil // File not found is not an error for initial state
		}
		return state, fmt.Errorf("error reading state file '%s': %w", filePath, err)
	}

	parts := strings.Fields(string(data))
	if len(parts) == 2 {
		var readInode uint64
		var readTime time.Time
		// Use Scanf for simple space-separated format
		_, scanErr := fmt.Sscanf(string(data), "%d %s", &readInode, &parts[1])
		if scanErr == nil {
			// Parse the timestamp using the specific Go layout for RFC3339 Nano UTC
			readTime, err = time.Parse(rfc3339NanoUTC, parts[1])
			if err == nil {
				state.Inode = readInode
				state.Timestamp = readTime
				return state, nil // Successfully parsed state
			} else {
				// Error parsing timestamp
				return state, fmt.Errorf("failed to parse timestamp in state file '%s': %w", filePath, err)
			}
		} else {
			// Error scanning inode/timestamp string
			return state, fmt.Errorf("failed to parse state file format in '%s': %w", filePath, scanErr)
		}
	} else {
		// Invalid number of fields
		return state, fmt.Errorf("invalid format in state file '%s': expected 2 fields, got %d", filePath, len(parts))
	}
}

// WriteState writes the current state (inode, timestamp) to the specified file.
func WriteState(filePath string, currentState State) error {
	// Format: INODE TIMESTAMP (RFC3339Nano UTC)
	newState := fmt.Sprintf("%d %s", currentState.Inode, currentState.Timestamp.Format(rfc3339NanoUTC))
	err := os.WriteFile(filePath, []byte(newState), 0644)
	if err != nil {
		return fmt.Errorf("failed to write state file '%s': %w", filePath, err)
	}
	return nil
}
