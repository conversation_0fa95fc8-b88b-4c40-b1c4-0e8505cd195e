package utils

import (
	"bufio"
	"fmt"
	"os"
	"runtime"
	"strconv"
	"strings"
)

// GetTotalSystemMemoryMB attempts to get the total system memory in MB.
// Currently supports Linux by reading /proc/meminfo.
// Returns total memory in MB and nil error on success.
// Returns 0 and an error on failure or unsupported OS.
func GetTotalSystemMemoryMB() (uint64, error) {
	if runtime.GOOS != "linux" {
		return 0, fmt.Errorf("getting system memory is currently only supported on Linux (reads /proc/meminfo)")
	}

	file, err := os.Open("/proc/meminfo")
	if err != nil {
		return 0, fmt.Errorf("failed to open /proc/meminfo: %w", err)
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := scanner.Text()
		if strings.HasPrefix(line, "MemTotal:") {
			fields := strings.Fields(line)
			if len(fields) >= 2 {
				val, err := strconv.ParseUint(fields[1], 10, 64)
				if err != nil {
					return 0, fmt.E<PERSON>rf("failed to parse MemTotal value '%s': %w", fields[1], err)
				}
				// Value is in kB, convert to MB
				return val / 1024, nil
			}
		}
	}

	if err := scanner.Err(); err != nil {
		return 0, fmt.Errorf("error scanning /proc/meminfo: %w", err)
	}

	return 0, fmt.Errorf("could not find MemTotal in /proc/meminfo")
}
