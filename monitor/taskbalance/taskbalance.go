package taskbalance

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"math"
	"net/http"
	"net/url"
	"strings"
	"time"

	"monitor/config"
	"monitor/logger"
)

const (
	clusterStateAPIPath      = "/api/clusterStates"
	processInfoAPIPath       = "/api/Workers/getProcessInfo"
	defaultAPIScheme         = "http" // Or make this configurable if https is also an option
	defaultClusterStateIndex = "1"    // Default index for clusterStates API
)

// EngineProcessInfo stores the running number of tasks for an engine from getProcessInfo API
type EngineProcessInfo struct {
	RunningNum int `json:"runningNum"`
}

// ProcessInfoApiResponseData maps process_id to its EngineProcessInfo
type ProcessInfoApiResponseData map[string]EngineProcessInfo

// ProcessInfoApiResponse is the structure for /api/Workers/getProcessInfo response
type ProcessInfoApiResponse struct {
	ReqID string                     `json:"reqId"`
	TS    int64                      `json:"ts"`
	Code  string                     `json:"code"`
	Data  ProcessInfoApiResponseData `json:"data"`
}

// --- Structures for /api/clusterStates API Response ---

// ClusterSystemInfo holds system information for a cluster node
type ClusterSystemInfo struct {
	Hostname  string `json:"hostname"`
	ProcessID string `json:"process_id"`
	// Add other fields like uuid, ip if needed for logging/identification
}

// ClusterEngineInfo holds engine status
type ClusterEngineInfo struct {
	Status string `json:"status"` // e.g., "running"
}

// ClusterStateItem represents a single item in the clusterStates API response
type ClusterStateItem struct {
	ID         string            `json:"id"`
	SystemInfo ClusterSystemInfo `json:"systemInfo"`
	Engine     ClusterEngineInfo `json:"engine"`
	// Add other fields like management, apiServer if needed
}

// ClusterStateData holds the list of items from clusterStates API
type ClusterStateData struct {
	Total int                `json:"total"`
	Items []ClusterStateItem `json:"items"`
}

// ClusterStatesApiResponse is the structure for /api/clusterStates response
type ClusterStatesApiResponse struct {
	ReqID string           `json:"reqId"`
	TS    int64            `json:"ts"`
	Code  string           `json:"code"`
	Data  ClusterStateData `json:"data"`
}

// ActiveEngine stores ProcessID and Hostname for an active engine
type ActiveEngine struct {
	ProcessID string
	Hostname  string
}

// --- Helper function to make HTTP GET requests ---
func makeAPIRequest(baseURL *url.URL, path string, accessToken string, queryParams url.Values) ([]byte, error) {
	apiURL := *baseURL // Create a mutable copy
	apiURL.Path = path

	if queryParams == nil {
		queryParams = url.Values{}
	}
	queryParams.Set("access_token", accessToken)
	apiURL.RawQuery = queryParams.Encode()

	client := &http.Client{Timeout: 15 * time.Second} // Increased timeout slightly
	req, err := http.NewRequest("GET", apiURL.String(), nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create API request for %s: %w", apiURL.String(), err)
	}

	logger.LogMonitor("DEBUG", fmt.Sprintf("Task Balance: Calling API: %s", apiURL.String()))

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to call API %s: %w", apiURL.String(), err)
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read API response body from %s: %w", apiURL.String(), err)
	}

	if resp.StatusCode != http.StatusOK {
		return body, fmt.Errorf("API request to %s failed with status %d. Response: %s", apiURL.String(), resp.StatusCode, string(body))
	}
	return body, nil
}

// --- Function to get active engines ---
func getActiveEngines(cfg *config.Config, baseAPIURL *url.URL) ([]ActiveEngine, error) {
	clusterStatesQuery := url.Values{}
	clusterStatesQuery.Set("index", defaultClusterStateIndex) // As per user provided curl

	body, err := makeAPIRequest(baseAPIURL, clusterStateAPIPath, cfg.TaskBalanceAccessToken, clusterStatesQuery)
	if err != nil {
		logger.LogMonitor("ERROR", fmt.Sprintf("Task Balance: Failed to get cluster states: %v", err))
		return nil, err
	}

	var clusterResponse ClusterStatesApiResponse
	if err := json.Unmarshal(body, &clusterResponse); err != nil {
		logger.LogMonitor("ERROR", fmt.Sprintf("Task Balance: Failed to unmarshal cluster states API response JSON: %v. Response: %s", err, string(body)))
		return nil, err
	}

	if clusterResponse.Code != "ok" {
		err := fmt.Errorf("cluster states API returned non-ok code: '%s'. Response: %s", clusterResponse.Code, string(body))
		logger.LogMonitor("ERROR", fmt.Sprintf("Task Balance: %v", err))
		return nil, err
	}

	var activeEngines []ActiveEngine
	if clusterResponse.Data.Items == nil {
		logger.LogMonitor("WARNING", "Task Balance: Cluster states API returned no items in data.")
		return activeEngines, nil // Return empty slice, not an error
	}

	for _, item := range clusterResponse.Data.Items {
		if strings.ToLower(item.Engine.Status) == "running" && item.SystemInfo.ProcessID != "" {
			activeEngines = append(activeEngines, ActiveEngine{
				ProcessID: item.SystemInfo.ProcessID,
				Hostname:  item.SystemInfo.Hostname,
			})
			logger.LogMonitor("DEBUG", fmt.Sprintf("Task Balance: Found active engine: Hostname='%s', ProcessID='%s'", item.SystemInfo.Hostname, item.SystemInfo.ProcessID))
		} else {
			logger.LogMonitor("DEBUG", fmt.Sprintf("Task Balance: Skipping engine: Hostname='%s', ProcessID='%s', EngineStatus='%s'", item.SystemInfo.Hostname, item.SystemInfo.ProcessID, item.Engine.Status))
		}
	}

	if len(activeEngines) == 0 {
		logger.LogMonitor("INFO", "Task Balance: No actively running engines found from cluster states API.")
	}
	return activeEngines, nil
}

// CheckTaskBalance performs the task balance check.
func CheckTaskBalance(cfg *config.Config) {
	// Construct base API URL from hostport
	// Ensure scheme is prepended if not present in hostport
	hostPort := cfg.TaskBalanceHostPort
	if !strings.HasPrefix(hostPort, "http://") && !strings.HasPrefix(hostPort, "https://") {
		hostPort = defaultAPIScheme + "://" + hostPort
	}
	baseAPIURL, err := url.Parse(hostPort)
	if err != nil {
		logger.LogMonitor("ERROR", fmt.Sprintf("Task Balance: Invalid API HostPort '%s': %v", cfg.TaskBalanceHostPort, err))
		return
	}

	activeEngines, err := getActiveEngines(cfg, baseAPIURL)
	if err != nil {
		// Error already logged by getActiveEngines
		return
	}

	if len(activeEngines) == 0 {
		logger.LogMonitor("INFO", "Task Balance: No active engines to monitor, skipping task count check.")
		return
	}

	var processIDsToQuery []string
	engineHostnames := make(map[string]string) // Map ProcessID to Hostname
	for _, engine := range activeEngines {
		processIDsToQuery = append(processIDsToQuery, engine.ProcessID)
		engineHostnames[engine.ProcessID] = engine.Hostname
	}

	// The process_id parameter needs to be a JSON array string, e.g., ["id1","id2"]
	processIDsJSON, err := json.Marshal(processIDsToQuery)
	if err != nil {
		logger.LogMonitor("ERROR", fmt.Sprintf("Task Balance: Failed to marshal process IDs for getProcessInfo API: %v", err))
		return
	}
	processInfoQuery := url.Values{}
	processInfoQuery.Set("process_id", string(processIDsJSON))

	body, err := makeAPIRequest(baseAPIURL, processInfoAPIPath, cfg.TaskBalanceAccessToken, processInfoQuery)
	if err != nil {
		logger.LogMonitor("ERROR", fmt.Sprintf("Task Balance: Failed to get process info: %v", err))
		return
	}

	var processInfoResponse ProcessInfoApiResponse
	if err := json.Unmarshal(body, &processInfoResponse); err != nil {
		logger.LogMonitor("ERROR", fmt.Sprintf("Task Balance: Failed to unmarshal process info API response JSON: %v. Response: %s", err, string(body)))
		return
	}

	if processInfoResponse.Code != "ok" {
		logger.LogMonitor("ERROR", fmt.Sprintf("Task Balance: Process info API returned non-ok code: '%s'. Response: %s", processInfoResponse.Code, string(body)))
		return
	}

	if len(processInfoResponse.Data) == 0 {
		logger.LogMonitor("WARNING", fmt.Sprintf("Task Balance: Process info API returned no data for process IDs: %v. Response: %s", processIDsToQuery, string(body)))
		return
	}

	engineTaskCounts := make(map[string]int) // Map ProcessID to task count
	totalRunningTasks := 0
	monitoredEngineCount := 0

	for _, processID := range processIDsToQuery {
		engineData, ok := processInfoResponse.Data[processID]
		if !ok {
			hostname := engineHostnames[processID] // Get hostname for logging
			logger.LogMonitor("WARNING", fmt.Sprintf("Task Balance: Process ID '%s' (Hostname: '%s') not found in process info API response data.", processID, hostname))
			continue
		}
		engineTaskCounts[processID] = engineData.RunningNum
		totalRunningTasks += engineData.RunningNum
		monitoredEngineCount++
	}

	if monitoredEngineCount == 0 {
		logger.LogMonitor("INFO", "Task Balance: No process IDs from the active list were found in the process info API response. Nothing to compare.")
		return
	}

	averageRunningTasks := float64(totalRunningTasks) / float64(monitoredEngineCount)
	logger.LogMonitor("INFO", fmt.Sprintf("Task Balance: Monitored %d engines. Total tasks: %d. Average tasks per engine: %.2f", monitoredEngineCount, totalRunningTasks, averageRunningTasks))

	thresholdN := float64(cfg.TaskBalanceThresholdN)

	for processID, runningNum := range engineTaskCounts {
		difference := math.Abs(float64(runningNum) - averageRunningTasks)

		if difference > thresholdN {
			comparisonWord := "大"
			if float64(runningNum) < averageRunningTasks {
				comparisonWord = "小"
			}
			engineDisplayName := engineHostnames[processID] // Use stored hostname
			if engineDisplayName == "" {                    // Fallback if hostname wasn't found for some reason
				engineDisplayName = processID
			}

			alertMessage := fmt.Sprintf(
				"引擎 %s (ID: %s) 当前运行任务数为 %d，比当前总运行任务数的平均值 %.2f %s %.2f，任务调度出现不均衡，请及时关注",
				engineDisplayName,
				processID, // Include ProcessID for clarity
				runningNum,
				averageRunningTasks,
				comparisonWord,
				difference,
			)
			logger.LogMonitor("MAJOR", fmt.Sprintf("Task Balance Alert: %s", alertMessage))
		}
	}

	logger.LogMonitor("INFO", "Task Balance: Check completed.")
}
