# MongoDB 监控器用户指南

本指南将帮助您了解如何使用 MongoDB 监控器来监控您的 MongoDB 实例和相关系统资源。

## 简介

MongoDB 监控器是一个 Go 应用程序，旨在监控以下方面：

1.  **MongoDB 日志文件**: 实时分析日志，报告指定严重级别的错误和警告。
2.  **Java 应用程序堆内存**: 监控特定 Java 进程的堆内存使用率，并在超过阈值时发出告警并终止进程。
3.  **进程 CPU 使用率**: 监控一个或多个进程的聚合 CPU 使用率，并在持续超过阈值时发出告警。
4.  **MongoDB 健康状况**: 连接到 MongoDB 实例，检查其状态、断言、队列、锁和复制延迟。
5.  **MongoDB 内存使用**: 监控 MongoDB 进程的物理内存使用，并与系统总内存进行比较，在超过阈值时发出告警。

所有监控结果都会写入指定的中央日志文件。您可以精确控制启用哪些监控项。

## 前提条件

在运行监控器之前，请确保满足以下条件：

*   **访问权限**: 运行监控器的用户需要：
    *   对监控器工作目录（用于存放状态文件）的读写权限。
    *   对输出日志文件 (`-logfile`) 的写入权限。
    *   对 MongoDB 日志文件 (`-mongo-log`) 的读取权限（如果启用了 `log` 监控）。
*   **系统命令**: 操作系统需要提供 `pgrep`, `ps`, `kill` 命令。这些通常在标准的 Linux/macOS 环境中可用。
*   **堆内存监控 (`heap`)**:
    *   目标服务器上必须安装 JDK，且 `jstat` 和 `jcmd` 命令可用（或通过 `-jdk-path` 指定路径）。
    *   运行监控器的用户需要有权限执行 `jstat`, `jcmd` 和 `kill` 来操作目标 Java 进程。
*   **CPU 监控 (`cpu`)**:
    *   运行监控器的用户需要有权限执行 `ps` 来查看目标进程的 CPU 使用率。
*   **MongoDB 健康监控 (`mongohealth`)**:
    *   监控器运行的环境需要能够网络连接到目标 MongoDB 实例。
    *   如果 MongoDB 需要认证，需要提供有足够权限（执行 `serverStatus`, `replSetGetStatus`）的用户名和密码。
*   **MongoDB 内存监控 (`mongomemory`)**:
    *   与 `mongohealth` 类似，需要网络连接和可能的认证凭据。
    *   监控器需要能读取系统总内存信息 (通常通过 `/proc/meminfo` 或类似机制)。

## 运行监控器

1.  **获取可执行文件**: 您需要有编译好的 `monitor` 可执行文件。
2.  **执行命令**: 在终端中运行监控器，并使用命令行标志进行配置。

**基本命令结构:**

```bash
./monitor -monitor-types <type1,type2,...> [其他选项...]
```

*   `-monitor-types`: **必需**。指定要启用的监控类型，用逗号分隔。可用类型包括：
    *   `log`: 监控 MongoDB 日志。
    *   `heap`: 监控 Java 堆内存。
    *   `cpu`: 监控进程 CPU 使用率。
    *   `mongohealth`: 监控 MongoDB 健康状况。
    *   `mongomemory`: 监控 MongoDB 内存使用。
    *   **默认**: 如果不指定，仅启用 `log` 监控。

**通用选项:**

*   `-logfile <路径>`: 指定输出监控日志的文件路径 (默认: `./monitor.log`)。
*   `-project-code <代码>`: 在监控日志中添加项目标识符 (默认: `DEFAULT_PROJECT`)。
*   `-h` 或 `--help`: 显示所有可用选项和帮助信息。

## 配置各项监控

### 1. 日志监控 (`log`)

当 `-monitor-types` 包含 `log` 时启用。

*   `-mongo-log <路径>`: **必需**。指定要监控的 MongoDB 日志文件路径 (默认: `/var/log/mongodb/mongod.log`)。
*   `-format <json|text>`: 指定 MongoDB 日志格式 (默认: `text`)。
*   `-state-file <路径>`: 存储日志处理状态的文件路径，用于防止重启后重复处理 (默认: `./mongo_log_state.json`)。
*   `-severity <级别>` 或 `-S <级别>`: (可多次指定) 要报告的日志严重级别。可选值: `F` (Fatal), `E` (Error), `W` (Warning), `I` (Informational), `D` (Debug)。(默认: `F,E,W`)。

**示例:**

```bash
# 监控 /data/db/mongod.log，JSON 格式，只报告 F 和 E 级别
./monitor -monitor-types log \
    -mongo-log /data/db/mongod.log \
    -format json \
    -S F -S E
```

### 2. 堆内存监控 (`heap`)

当 `-monitor-types` 包含 `heap` 时启用。

*   `-heap-process <模式>`: **必需**。用于 `pgrep -f` 查找目标 Java 进程的模式 (例如: `my-application.jar`, `com.example.MainClass`)。
*   `-heap-threshold <百分比>`: 堆内存使用率阈值。超过此值将触发 MAJOR 告警并**终止进程** (默认: `90.0`)。
*   `-jdk-path <路径>`: 如果 `jstat`/`jcmd` 不在系统 PATH 中，指定 JDK 的 `bin` 目录路径。
*   `-state-dir <目录路径>`: 用于存储堆内存监控状态文件的目录 (用于连续告警逻辑) (默认: `.`)。

**示例:**

```bash
# 监控包含 "my-app.jar" 的 Java 进程，阈值为 85%
./monitor -monitor-types heap \
    -heap-process my-app.jar \
    -heap-threshold 85
```

### 3. CPU 使用率监控 (`cpu`)

当 `-monitor-types` 包含 `cpu` 时启用。

*   `-process <模式>` 或 `-p <模式>`: **必需**。用于 `pgrep -f` 查找目标进程的模式。
*   `-major-threshold <百分比>` 或 `-M <百分比>`: 触发 MAJOR 告警的聚合 CPU 使用率阈值 (默认: `90.0`)。
*   `-minor-threshold <百分比>` 或 `-m <百分比>`: 触发 MINOR 告警的聚合 CPU 使用率阈值 (默认: `75.0`)。
*   `-state-dir <目录路径>`: 用于存储 CPU 监控状态文件的目录 (用于连续告警逻辑) (默认: `.`)。 **注意**: 只有当 CPU 使用率**连续**高于阈值时才会生成告警日志。

**示例:**

```bash
# 监控所有包含 "data-processor" 的进程
# 次要阈值 70%，主要阈值 90%
./monitor -monitor-types cpu \
    -p data-processor \
    -m 70 -M 90
```

### 4. MongoDB 健康监控 (`mongohealth`)

当 `-monitor-types` 包含 `mongohealth` 时启用。

*   `-mongo-host <主机名/IP>`: MongoDB 服务器地址 (默认: `localhost`)。
*   `-mongo-port <端口>`: MongoDB 服务器端口 (默认: `27017`)。
*   `-mongo-user <用户名>`: MongoDB 认证用户名 (可选)。
*   `-mongo-pass <密码>`: MongoDB 认证密码 (可选)。
*   `-mongo-authdb <数据库>`: MongoDB 认证数据库 (通常是 `admin`) (默认: `admin`)。
*   `-queued-op-threshold <数量>`: 等待队列中操作数的告警阈值 (默认: `10`)。
*   `-lock-pct-threshold-minor <百分比>`: 全局锁时间占比的 MINOR 告警阈值 (默认: `10.0`)。
*   `-lock-pct-threshold-major <百分比>`: 全局锁时间占比的 MAJOR 告警阈值 (默认: `20.0`)。
*   `-repl-lag-threshold-minor <秒>`: 复制延迟的 MINOR 告警阈值 (默认: `60`)。
*   `-repl-lag-threshold-major <秒>`: 复制延迟的 MAJOR 告警阈值 (默认: `300`)。

**示例:**

```bash
# 检查远程认证的 MongoDB，调整复制延迟阈值
./monitor -monitor-types mongohealth \
    -mongo-host db.example.com \
    -mongo-port 27018 \
    -mongo-user monitor \
    -mongo-pass "p@sswOrd" \
    -mongo-authdb admin \
    -repl-lag-threshold-minor 120 \
    -repl-lag-threshold-major 600
```

### 5. MongoDB 内存监控 (`mongomemory`)

当 `-monitor-types` 包含 `mongomemory` 时启用。

*   `-mongo-host <主机名/IP>`, `-mongo-port <端口>`, `-mongo-user <用户名>`, `-mongo-pass <密码>`, `-mongo-authdb <数据库>`: 与 `mongohealth` 共用相同的 MongoDB 连接参数。
*   `-mem-major-threshold <百分比>`: 物理内存占用系统总内存百分比的 MAJOR 告警阈值 (默认: `80.0`)。
*   `-mem-minor-threshold <百分比>`: 物理内存占用系统总内存百分比的 MINOR 告警阈值 (默认: `70.0`)。
*   `-mem-state-dir <目录路径>`: 用于存储内存监控状态文件的目录 (用于连续告警逻辑) (默认: `.`，与 `-state-dir` 不同，用于区分)。 **注意**: 只有当内存使用率**连续**高于阈值时才会生成告警日志。

**示例:**

```bash
# 监控本地 MongoDB 的内存使用，告警阈值为 65% 和 85%
./monitor -monitor-types mongomemory \
    -mem-minor-threshold 65 \
    -mem-major-threshold 85
```

## 组合使用示例

您可以同时启用多个监控类型：

```bash
# 监控日志、CPU 和 MongoDB 健康状况
./monitor -monitor-types log,cpu,mongohealth \
    -logfile /var/log/app_monitor.log \
    -project-code MYAPP \
    -mongo-log /app/logs/mongod.log \
    -p my-app-process -m 70 -M 90 \
    -mongo-host mongodb.internal
```

## 查看帮助

要查看所有可用的命令行选项及其默认值，请运行：

```bash
./monitor -h
```

## 日志输出

监控器会将所有发现的问题或状态变更记录到由 `-logfile` 指定的文件中。日志条目通常包含时间戳、项目代码、监控类型、严重级别 (如 MAJOR, MINOR, INFO) 和详细信息。

例如：

```
2023-10-27 10:30:00 MYAPP CPU MAJOR - Aggregated CPU usage for pattern 'my-app-process' is 95.5% (Major Threshold: 90.0%)
2023-10-27 10:31:00 MYAPP LOG MINOR - MongoDB Log [/app/logs/mongod.log]: W NETWORK  [conn1] Error receiving request from client: SSLHandshakeFailed: SSL peer certificate validation failed: self signed certificate
2023-10-27 10:32:00 MYAPP MONGOHEALTH INFO - MongoDB health check OK (mongodb.internal:27017)
```

请定期检查此日志文件以了解系统状态。 