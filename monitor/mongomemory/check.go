package mongomemory

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readpref"

	"monitor/config"
	"monitor/logger"
	"monitor/utils"
)

const (
	StatusOK       = 0
	StatusWarning  = 1
	StatusCritical = 2
	StatusUnknown  = 3

	defaultStateFilename = "mongomemory.state"
)

// Structure to hold the state saved to the file
type checkState struct {
	PreviousStatus int `json:"previous_status"`
}

type ServerStatus struct {
	Mem struct {
		Resident uint64 `bson:"resident"` // Resident memory in MB
	} `bson:"mem"`
}

// Helper function to read state from file
func readCheckState(filePath string) (checkState, error) {
	state := checkState{PreviousStatus: StatusOK} // Default to OK if file not found or error
	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			return state, nil // File not found is not an error here, just means previous state was OK
		}
		return state, fmt.Errorf("failed to read state file %s: %w", filePath, err)
	}

	if len(data) == 0 { // Handle empty file case
		return state, nil
	}

	err = json.Unmarshal(data, &state)
	if err != nil {
		// If unmarshal fails, assume previous state was OK and log a warning
		fmt.Fprintf(os.Stderr, "Warning: Failed to parse state file %s (%v), assuming previous state OK.\n", filePath, err)
		state.PreviousStatus = StatusOK
		// We proceed, but don't return the JSON error itself as fatal
		return state, nil
	}
	return state, nil
}

// Helper function to write state to file
func writeCheckState(filePath string, status int) error {
	state := checkState{PreviousStatus: status}
	data, err := json.Marshal(state)
	if err != nil {
		return fmt.Errorf("failed to marshal state (%d): %w", status, err)
	}
	err = ioutil.WriteFile(filePath, data, 0644)
	if err != nil {
		return fmt.Errorf("failed to write state file %s: %w", filePath, err)
	}
	return nil
}

// CheckMongoMemory performs the MongoDB memory usage check with 2-consecutive-run alert logic.
func CheckMongoMemory(cfg config.Config) (int, string) {
	mongoURI := buildMongoURI(cfg)

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	clientOpts := options.Client().ApplyURI(mongoURI)
	client, err := mongo.Connect(ctx, clientOpts)
	if err != nil {
		errMsg := fmt.Sprintf("MONGOMEMORY MAJOR - Failed to connect to MongoDB (%s): %s", mongoURI, err)
		logger.LogMonitor("MAJOR", errMsg)
		return StatusCritical, errMsg
	}
	defer func() { _ = client.Disconnect(context.Background()) }()

	if err := client.Ping(ctx, readpref.Primary()); err != nil {
		errMsg := fmt.Sprintf("MONGOMEMORY MAJOR - Failed to ping MongoDB primary: %s", err)
		logger.LogMonitor("MAJOR", errMsg)
		return StatusCritical, errMsg
	}

	var status ServerStatus
	err = client.Database("admin").RunCommand(ctx, bson.D{{"serverStatus", 1}}).Decode(&status)
	if err != nil {
		errMsg := fmt.Sprintf("MONGOMEMORY MAJOR - Failed to execute serverStatus: %s", err)
		logger.LogMonitor("MAJOR", errMsg)
		return StatusCritical, errMsg
	}

	residentMB := status.Mem.Resident

	totalSystemMemoryMB, err := utils.GetTotalSystemMemoryMB()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Warning: Failed to get total system memory (%s), proceeding without percentage calculation.\n", err)
		totalSystemMemoryMB = 0
	}

	var percentage float64 = -1
	if totalSystemMemoryMB > 0 {
		percentage = (float64(residentMB) / float64(totalSystemMemoryMB)) * 100
	}

	// --- 2. Determine Current Potential Status ---
	currentPotentialStatus := StatusOK
	baseMessage := fmt.Sprintf("Resident memory: %d MB", residentMB)
	if percentage >= 0 {
		baseMessage += fmt.Sprintf(" (%.2f%% of %d MB system total)", percentage, totalSystemMemoryMB)
		if percentage >= cfg.MemMajorThreshold {
			currentPotentialStatus = StatusCritical
		} else if percentage >= cfg.MemMinorThreshold {
			currentPotentialStatus = StatusWarning
		}
	} else {
		baseMessage += " (System total memory unavailable, percentage thresholds skipped)"
	}

	// --- 3. Apply 2-Consecutive-Run Logic using State File ---
	stateFilePath := filepath.Join(cfg.MemStateDir, defaultStateFilename)
	prevState, err := readCheckState(stateFilePath)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Warning: Error reading state file %s: %v\n", stateFilePath, err)
	}
	previousStatus := prevState.PreviousStatus

	actualStatus := StatusOK
	statusString := "OK"
	message := "OK - " + baseMessage
	recoveryMsg := ""
	logOnlyMessage := ""

	switch currentPotentialStatus {
	case StatusOK:
		actualStatus = StatusOK
		statusString = "OK"
		if previousStatus != StatusOK {
			recoveryMsg = " (Recovered)"
			logOnlyMessage = "MongoDB Memory usage recovered to OK."
			logger.LogMonitor("INFO", logOnlyMessage)
			err = os.Remove(stateFilePath)
			if err != nil && !os.IsNotExist(err) {
				fmt.Fprintf(os.Stderr, "Warning: Failed to remove state file %s on recovery: %v\n", stateFilePath, err)
			}
		}
		message = "OK - " + baseMessage + recoveryMsg

	case StatusWarning:
		if previousStatus == StatusWarning { // Second consecutive warning
			actualStatus = StatusWarning
			statusString = "WARNING"
			message = "WARNING - " + baseMessage
			logOnlyMessage = baseMessage
			logger.LogMonitor("MINOR", logOnlyMessage)
		} else { // First warning
			actualStatus = StatusOK
			statusString = "OK"
			message = "OK - " + baseMessage + " (First occurrence above Warning threshold)"
			logOnlyMessage = "MongoDB Memory usage above Warning threshold (first occurrence). Alert suppressed."
			logger.LogMonitor("INFO", logOnlyMessage)
		}
		err = writeCheckState(stateFilePath, currentPotentialStatus)
		if err != nil {
			fmt.Fprintf(os.Stderr, "Warning: Failed to write state file %s: %v\n", stateFilePath, err)
		}

	case StatusCritical:
		if previousStatus == StatusCritical { // Second consecutive critical
			actualStatus = StatusCritical
			statusString = "CRITICAL"
			message = "CRITICAL - " + baseMessage
			logOnlyMessage = baseMessage
			logger.LogMonitor("MAJOR", logOnlyMessage)
		} else { // First critical
			actualStatus = StatusOK
			statusString = "OK"
			message = "OK - " + baseMessage + " (First occurrence above Critical threshold)"
			logOnlyMessage = "MongoDB Memory usage above Critical threshold (first occurrence). Alert suppressed."
			logger.LogMonitor("INFO", logOnlyMessage)
		}
		err = writeCheckState(stateFilePath, currentPotentialStatus)
		if err != nil {
			fmt.Fprintf(os.Stderr, "Warning: Failed to write state file %s: %v\n", stateFilePath, err)
		}
	}

	// --- 4. Format Performance Data (Same as before) ---
	perfData := fmt.Sprintf("resident_mb=%d;;;; total_system_mb=%d;;;; percentage=%.2f;%.1f;%.1f;;",
		residentMB,
		totalSystemMemoryMB,
		percentage,
		cfg.MemMinorThreshold,
		cfg.MemMajorThreshold,
	)
	if percentage < 0 {
		perfData = fmt.Sprintf("resident_mb=%d;;;; total_system_mb=%d;;;;",
			residentMB,
			totalSystemMemoryMB,
		)
	}

	// --- 5. Return Final Status and Message ---
	finalOutput := fmt.Sprintf("MONGOMEMORY %s | %s%s", statusString, message, perfData)
	return actualStatus, finalOutput
}

// buildMongoURI constructs the MongoDB connection string from config values.
func buildMongoURI(cfg config.Config) string {
	var sb strings.Builder
	sb.WriteString("mongodb://")

	// Add user/pass if provided
	if cfg.MongoUser != "" && cfg.MongoPass != "" {
		sb.WriteString(cfg.MongoUser)
		sb.WriteString(":")
		sb.WriteString(cfg.MongoPass) // Consider URL encoding if password contains special chars
		sb.WriteString("@")
	}

	// Add host and port
	sb.WriteString(cfg.MongoHost)
	sb.WriteString(":")
	sb.WriteString(fmt.Sprintf("%d", cfg.MongoPort))

	// Add authSource if user/pass provided
	if cfg.MongoUser != "" && cfg.MongoPass != "" {
		sb.WriteString("/?authSource=")
		sb.WriteString(cfg.MongoAuthDB)
	}

	return sb.String()
}

func getStatusString(status int) string {
	switch status {
	case StatusOK:
		return "OK"
	case StatusWarning:
		return "WARNING"
	case StatusCritical:
		return "CRITICAL"
	default:
		return "UNKNOWN"
	}
}
