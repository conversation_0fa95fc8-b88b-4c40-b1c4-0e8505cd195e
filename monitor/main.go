package main

import (
	"fmt"
	"io/ioutil"
	"path/filepath"
	"strconv"

	// "io"

	"monitor/config"
	"monitor/cpu"
	"monitor/heap"
	"monitor/logger"
	"monitor/mongohealth"
	"monitor/mongomemory"
	"monitor/processor"
	"monitor/state"
	"monitor/taskbalance"
	"os"
	"syscall"
	"time"

	"github.com/nightlyone/lockfile"
)

const lockFileName = "monitor.lock"

func main() {
	// Define the lock file path once
	lockFilePath := filepath.Join(os.TempDir(), lockFileName)

	// Attempt to create a lock file to ensure single instance
	lock, err := lockfile.New(lockFilePath)
	if err != nil {
		fmt.Fprintf(os.Stderr, "FATAL: Cannot init lockfile: %v\n", err)
		os.Exit(1)
	}
	err = lock.TryLock()
	if err != nil {
		fmt.Fprintf(os.Stderr, "INFO: Another instance is already running (lockfile error: %v). Exiting.\n", err)
		os.Exit(0) // Exit gracefully if another instance is running
	}
	defer lock.Unlock() // Ensure the lock is released when main exits

	// Write PID to lock file for easier identification of the running process
	pid := os.Getpid()
	if err := ioutil.WriteFile(lockFilePath, []byte(strconv.Itoa(pid)), 0644); err != nil {
		fmt.Fprintf(os.Stderr, "WARNING: Failed to write PID to lockfile: %v\n", err)
		// Continue execution even if PID write fails, as the lock is still held.
	}

	// 1. Parse Configuration
	cfg := config.ParseFlags()

	// 2. Initialize Logger
	monitorLogFile, err := logger.InitLogger(cfg.LogfilePath, cfg.ProjectCode)
	if err != nil {
		fmt.Fprintf(os.Stderr, "FATAL: Cannot initialize logger: %v\n", err)
		os.Exit(1)
	}
	defer monitorLogFile.Close()

	// --- Perform Enabled Checks --- //

	// 3. Perform Heap Check (if enabled)
	if cfg.MonitorTypes["heap"] {
		logger.LogMonitor("INFO", "Starting Heap Check...")
		heap.CheckHeapUsage(cfg)
	}

	// 4. Perform CPU Check (if enabled)
	if cfg.MonitorTypes["cpu"] {
		logger.LogMonitor("INFO", "Starting CPU Check...")
		cpu.CheckCPUUsage(cfg)
	}

	// 5. Perform MongoDB Health Check (if enabled)
	if cfg.MonitorTypes["mongohealth"] {
		logger.LogMonitor("INFO", "Starting MongoDB Health Check...")
		mongohealth.CheckMongoHealth(cfg)
	}

	// ?. Perform MongoDB Memory Check (if enabled)
	if cfg.MonitorTypes["mongomemory"] {
		logger.LogMonitor("INFO", "Starting MongoDB Memory Check...")
		status, message := mongomemory.CheckMongoMemory(*cfg)
		fmt.Println(message)
		os.Exit(status)
	}

	// ?. Perform Task Balance Check (if enabled)
	if cfg.MonitorTypes["taskbalance"] {
		logger.LogMonitor("INFO", "Starting Task Balance Check...")
		taskbalance.CheckTaskBalance(cfg)
	}

	// 6. Perform MongoDB Log Monitoring (if enabled)
	if cfg.MonitorTypes["log"] {
		logger.LogMonitor("INFO", "Starting MongoDB Log Monitoring...")
		monitorMongoLog(cfg)
	}

	logger.LogMonitor("INFO", "Monitoring run finished (or no immediate-exit checks were enabled).")
}

// monitorMongoLog contains the logic previously in main for log monitoring
func monitorMongoLog(cfg *config.Config) {
	// Open MongoDB Log File
	mongoLog, err := os.Open(cfg.MongoLogPath)
	if err != nil {
		if !os.IsNotExist(err) {
			logger.LogMonitor("MAJOR", fmt.Sprintf("MongoDB log file '%s' unreadable: %v", cfg.MongoLogPath, err))
			return // Return instead of os.Exit
		}
		logger.LogMonitor("INFO", fmt.Sprintf("MongoDB log file '%s' not found. Will wait or process from beginning if state is invalid.", cfg.MongoLogPath))
		mongoLog = nil
	} else {
		defer mongoLog.Close()
	}

	// Handle State and Rotation
	lastState, err := state.ReadState(cfg.StateFilePath)
	if err != nil && !os.IsNotExist(err) {
		logger.LogMonitor("WARNING", fmt.Sprintf("Error reading state file '%s': %v. Processing from beginning.", cfg.StateFilePath, err))
	}

	currentInode := uint64(0)
	if mongoLog != nil {
		fileInfo, err := mongoLog.Stat()
		if err != nil {
			logger.LogMonitor("MAJOR", fmt.Sprintf("Failed to stat MongoDB log file '%s': %v", cfg.MongoLogPath, err))
			return // Return instead of os.Exit
		}
		stat_t, ok := fileInfo.Sys().(*syscall.Stat_t)
		if !ok {
			logger.LogMonitor("MAJOR", fmt.Sprintf("Failed to get inode for MongoDB log file '%s' (unsupported platform?)", cfg.MongoLogPath))
			return // Return instead of os.Exit
		}
		currentInode = stat_t.Ino
	}

	startTime := lastState.Timestamp
	if mongoLog == nil || currentInode == 0 || currentInode != lastState.Inode || lastState.Timestamp.IsZero() {
		if !lastState.Timestamp.IsZero() && currentInode != 0 && currentInode != lastState.Inode {
			logger.LogMonitor("INFO", fmt.Sprintf("MongoDB log rotation detected for %s (inode %d -> %d). Processing new file from beginning.", cfg.MongoLogPath, lastState.Inode, currentInode))
		} else if lastState.Timestamp.IsZero() {
			logger.LogMonitor("INFO", fmt.Sprintf("First run or state file invalid/not found for %s. Processing from beginning.", cfg.MongoLogPath))
		} else if mongoLog == nil {
			// Log already logged about file not found
		}
		startTime = time.Unix(0, 0)
	}

	// Write New State *Before* Processing
	if currentInode != 0 {
		newState := state.State{
			Inode:     currentInode,
			Timestamp: time.Now().UTC(),
		}
		if err := state.WriteState(cfg.StateFilePath, newState); err != nil {
			logger.LogMonitor("MAJOR", fmt.Sprintf("CRITICAL: Failed to write state file '%s': %v. Risk of reprocessing logs.", cfg.StateFilePath, err))
			// Continuing despite state write failure
		}
	}

	// Create Processor and Run
	proc, err := processor.NewProcessor(cfg, startTime)
	if err != nil {
		// Use fmt.Fprintf for fatal errors if logger might not be fully ready
		// But since logger init is step 2, we prefer logger here.
		logger.LogMonitor("FATAL", fmt.Sprintf("Error creating processor: %v", err))
		// Maybe still print to stderr as backup?
		fmt.Fprintf(os.Stderr, "FATAL: Error creating processor: %v\n", err)
		return // Return instead of os.Exit
	}

	err = proc.Run(mongoLog)
	if err != nil {
		logger.LogMonitor("WARNING", fmt.Sprintf("Log processing completed with error: %v", err))
	}
	logger.LogMonitor("INFO", "MongoDB Log Monitoring finished.")
}

// init function is no longer needed here as logger handles its own setup
// func init() {
// 	log.SetFlags(0)
// }
