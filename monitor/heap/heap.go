package heap

import (
	"fmt"
	"monitor/config"
	"monitor/executor"
	"monitor/logger"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
)

// Regex to validate the header of jstat -gc (more flexible)
var jstatGCHeaderRegex = regexp.MustCompile(`^\s*S0C\s+S1C\s+S0U\s+S1U\s+EC\s+EU\s+OC\s+OU\s+MC\s+MU\s+CCSC\s+CCSU\s+YGC\s+YGCT\s+FGC\s+FGCT\s+GCT\s*$`) // Note: Removed Timestamp as it might not always be present depending on JDK version/options

// CheckHeapUsage performs the heap utilization check for configured Java processes.
func CheckHeapUsage(cfg *config.Config) {
	if len(cfg.HeapProcessPatterns) == 0 {
		// logger.LogMonitor("DEBUG", "HeapCheck: No patterns configured.")
		return // No processes configured for heap check
	}

	pids, err := findJavaPIDs(cfg.HeapProcessPatterns)
	if err != nil {
		logger.LogMonitor("WARNING", fmt.Sprintf("HeapCheck: Failed to find Java PIDs: %v", err))
		return
	}

	if len(pids) == 0 {
		logger.LogMonitor("INFO", fmt.Sprintf("HeapCheck: No running Java processes found matching patterns: %v", cfg.HeapProcessPatterns))
		return
	}

	jstatCmd := findCommand("jstat", cfg.JDKBinPath)
	killCmd := findCommand("kill", "")             // kill is usually in PATH
	jcmdCmd := findCommand("jcmd", cfg.JDKBinPath) // Find jcmd for process check

	for _, pid := range pids {
		checkSingleProcess(cfg, pid, jstatCmd, killCmd, jcmdCmd)
	}
}

// findJavaPIDs uses pgrep to find PIDs of Java processes matching the given patterns.
func findJavaPIDs(patterns []string) ([]string, error) {
	pgrepCmd := findCommand("pgrep", "")
	var allPids []string

	for _, pattern := range patterns {
		// Use pgrep -f to match against the full command line
		stdout, _, err := executor.RunCommand(pgrepCmd, "-f", pattern)
		if err != nil {
			// pgrep returns non-zero exit status if no process is found, which is not a fatal error for us.
			if strings.Contains(err.Error(), "exit status 1") && stdout == "" {
				continue // No process found for this pattern
			}
			return nil, fmt.Errorf("pgrep failed for pattern '%s': %w", pattern, err)
		}

		pids := strings.Fields(stdout)
		allPids = append(allPids, pids...)
	}

	// Deduplicate PIDs in case patterns overlap
	if len(allPids) == 0 {
		return []string{}, nil
	}

	seen := make(map[string]struct{})
	uniquePids := []string{}
	for _, pid := range allPids {
		if _, ok := seen[pid]; !ok {
			seen[pid] = struct{}{}
			uniquePids = append(uniquePids, pid)
		}
	}

	return uniquePids, nil
}

// checkSingleProcess checks heap usage for a specific PID, matching the original script logic.
func checkSingleProcess(cfg *config.Config, pid string, jstatCmd string, killCmd string, jcmdCmd string) {
	// First, verify it's a Java process using jcmd (like the script)
	_, _, errJcmd := executor.RunCommand(jcmdCmd, pid, "VM.flags")
	if errJcmd != nil {
		// Log warning if jcmd fails (might not be Java, or permissions issue)
		logger.LogMonitor("WARNING", fmt.Sprintf("HeapCheck: '%s %s VM.flags' failed for PID %s (Not a Java process or permission issue?): %v", jcmdCmd, pid, pid, errJcmd))
		return
	}

	// Run jstat -gc <pid>
	stdout, stderr, err := executor.RunCommand(jstatCmd, "-gc", pid)
	if err != nil {
		// Log error but continue to next PID. Jstat might fail if process terminated.
		logger.LogMonitor("WARNING", fmt.Sprintf("HeapCheck: '%s -gc %s' failed: %v (stderr: %s)", jstatCmd, pid, err, stderr))
		return
	}

	lines := strings.Split(strings.TrimSpace(stdout), "\n")
	if len(lines) != 2 {
		logger.LogMonitor("WARNING", fmt.Sprintf("HeapCheck: Unexpected jstat -gc output format for PID %s (expected 2 lines):\n%s", pid, stdout))
		return
	}

	// Basic validation of the header line (more flexible regex)
	// if !jstatGCHeaderRegex.MatchString(lines[0]) {
	// 	logger.LogMonitor("WARNING", fmt.Sprintf("HeapCheck: jstat -gc output header mismatch for PID %s:\nExpected format like: S0C S1C...GCT\nGot: %s", pid, lines[0]))
	// 	return
	// }
	// Removing header check for robustness - rely on field count

	fields := strings.Fields(lines[1])
	// Expected order: S0C S1C S0U S1U EC EU OC OU MC MU CCSC CCSU YGC YGCT FGC FGCT GCT
	// Indices:        0   1   2   3   4  5  6  7  8  9  10   11   12  13   14  15   16
	// Need: EC (4), EU (5), OC (6), OU (7)
	if len(fields) < 8 {
		logger.LogMonitor("WARNING", fmt.Sprintf("HeapCheck: Not enough fields in jstat -gc data line for PID %s (need at least 8): %s", pid, lines[1]))
		return
	}

	// Parse required values (capacities and utilizations are in KB)
	ecStr, euStr, ocStr, ouStr := fields[4], fields[5], fields[6], fields[7]
	ec, errEC := strconv.ParseFloat(ecStr, 64)
	eu, errEU := strconv.ParseFloat(euStr, 64)
	oc, errOC := strconv.ParseFloat(ocStr, 64)
	ou, errOU := strconv.ParseFloat(ouStr, 64)

	if errEC != nil || errEU != nil || errOC != nil || errOU != nil {
		logger.LogMonitor("WARNING", fmt.Sprintf("HeapCheck: Failed to parse jstat -gc values for PID %s. EC:'%s', EU:'%s', OC:'%s', OU:'%s'. Errors: %v, %v, %v, %v",
			pid, ecStr, euStr, ocStr, ouStr, errEC, errEU, errOC, errOU))
		return
	}

	// Calculate overall heap usage percentage (like the script)
	used := eu + ou
	total := ec + oc

	if total == 0 {
		logger.LogMonitor("MAJOR", fmt.Sprintf("HeapCheck: %s PID %s calculated total heap capacity (EC+OC) is 0, cannot compute usage.", cfg.Hostname, pid))
		return
	}

	usagePct := (used / total) * 100
	thresholdFloat := float64(cfg.HeapThreshold)

	// Log and potentially kill based on threshold
	if usagePct >= thresholdFloat {
		message := fmt.Sprintf("%s PID %s Heap Usage: %.1f%% EXCEEDED threshold of %d%%. Killing process.", cfg.Hostname, pid, usagePct, cfg.HeapThreshold)
		logger.LogMonitor("MAJOR", message)

		// Kill the process
		// _, stderrKill, errKill := executor.RunCommand(killCmd, "-9", pid)
		// if errKill != nil {
		// 	// Match original script: Log kill failure as MAJOR
		// 	logger.LogMonitor("MAJOR", fmt.Sprintf("HeapCheck: Failed to kill process PID %s: %v (stderr: %s)", pid, errKill, stderrKill))
		// } else {
		// 	// Match original script: Log successful kill as MINOR
		// 	logger.LogMonitor("MINOR", fmt.Sprintf("HeapCheck: Process PID %s killed successfully due to heap overuse.", pid))
		// }
	} else {
		// Match original script: Log OK status as MINOR
		logger.LogMonitor("MINOR", fmt.Sprintf("%s PID %s Heap Usage: %.1f%% OK (Threshold: %d%%)", cfg.Hostname, pid, usagePct, cfg.HeapThreshold))
	}
}

// findCommand constructs the full path to a command, checking JDK path first if provided.
func findCommand(command string, jdkBinPath string) string {
	if jdkBinPath != "" {
		cmdPath := filepath.Join(jdkBinPath, command)
		if info, err := os.Stat(cmdPath); err == nil {
			// Check if it's executable (basic check)
			if info.Mode()&0111 != 0 {
				return cmdPath
			}
		}
	}
	// Fallback to searching in PATH
	cmdPath, err := exec.LookPath(command)
	if err != nil {
		// Log a warning? For now, just return the command name, assuming it's in PATH.
		// The executor will fail later if it's not found.
		logger.LogMonitor("WARNING", fmt.Sprintf("HeapCheck: Command '%s' not found in PATH or specified JDK path '%s'. Executor will likely fail.", command, jdkBinPath))
		return command
	}
	return cmdPath
}
 