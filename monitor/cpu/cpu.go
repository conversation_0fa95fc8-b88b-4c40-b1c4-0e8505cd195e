package cpu

import (
	"fmt"
	"monitor/config"
	"monitor/executor"
	"monitor/logger"
	"os"
	"path/filepath"
	"strconv"
	"strings"
)

// CheckCPUUsage performs the CPU utilization check for configured process patterns.
func CheckCPUUsage(cfg *config.Config) {
	if len(cfg.CPUProcessPatterns) == 0 {
		return // No patterns configured for CPU check
	}

	for _, pattern := range cfg.CPUProcessPatterns {
		checkSinglePattern(cfg, pattern)
	}
}

// checkSinglePattern handles the CPU check logic for one process pattern.
func checkSinglePattern(cfg *config.Config, pattern string) {
	pids, err := findPIDsForPattern(pattern)
	stateFileName := config.SanitizePatternForFilename(pattern) + "_highcpu"
	stateFilePath := filepath.Join(cfg.CPUStateDir, stateFileName)

	if err != nil {
		// Log error finding PIDs, but continue (it might be a transient pgrep issue)
		logger.LogMonitor("WARNING", fmt.Sprintf("CPUCheck: Failed to find PIDs for pattern '%s': %v", pattern, err))
		// Attempt to clean up state file if process check failed catastrophically
		removeStateFile(stateFilePath, pattern, "process check failure")
		return
	}

	if len(pids) == 0 {
		// Process not running - log MAJOR and ensure state file is removed
		logger.LogMonitor("MAJOR", fmt.Sprintf("%s Process matching pattern '%s' not running", cfg.Hostname, pattern))
		removeStateFile(stateFilePath, pattern, "process not running")
		return
	}

	aggregatedCPU, err := getAggregatedCPU(pids)
	if err != nil {
		logger.LogMonitor("WARNING", fmt.Sprintf("CPUCheck: Failed to get aggregated CPU for pattern '%s' (PIDs: %v): %v", pattern, pids, err))
		// Attempt to clean up state file if CPU check failed
		removeStateFile(stateFilePath, pattern, "CPU check failure")
		return
	}

	// Check thresholds and handle state
	stateFileExists := checkStateFile(stateFilePath)

	if aggregatedCPU >= cfg.CPUMajorThreshold {
		handleHighCPU(cfg, pattern, aggregatedCPU, "MAJOR", stateFilePath, stateFileExists)
	} else if aggregatedCPU >= cfg.CPUMinorThreshold {
		handleHighCPU(cfg, pattern, aggregatedCPU, "MINOR", stateFilePath, stateFileExists)
	} else {
		// CPU is OK, remove state file if it exists (indicates recovery)
		if stateFileExists {
			removeStateFile(stateFilePath, pattern, "CPU normal")
			// Optional: Log recovery message?
			// logger.LogMonitor("MINOR", fmt.Sprintf("%s %s Aggregated CPU Utilization %.1f%% back to normal", cfg.Hostname, pattern, aggregatedCPU))
		}
	}
}

// findPIDsForPattern uses pgrep to find PIDs matching a given pattern.
// Shares similarity with heap.findJavaPIDs - potential for refactoring later.
func findPIDsForPattern(pattern string) ([]string, error) {
	// Assuming pgrep is in PATH, executor.RunCommand handles errors
	stdout, _, err := executor.RunCommand("pgrep", "-f", pattern)
	if err != nil {
		// pgrep returns exit status 1 if no process is found. This is not an error here.
		if strings.Contains(err.Error(), "exit status 1") && stdout == "" {
			return []string{}, nil // No process found for this pattern
		}
		return nil, fmt.Errorf("pgrep failed for pattern '%s': %w", pattern, err)
	}
	return strings.Fields(stdout), nil
}

// getAggregatedCPU calculates the sum of CPU percentages for a list of PIDs.
func getAggregatedCPU(pids []string) (float64, error) {
	totalCPU := 0.0
	psCmd := "ps"

	for _, pid := range pids {
		// Get CPU for the specific PID. Use ps -p <pid> -o %cpu=
		// LC_NUMERIC=C is important for consistent decimal point format
		// We rely on executor.RunCommand which uses underlying OS execution.
		// Setting LC_NUMERIC directly in Go is complex, assume environment is okay or ps handles it.
		// TODO: Consider adding LC_NUMERIC=C to the command execution if parsing fails often.
		stdout, stderr, err := executor.RunCommand(psCmd, "-p", pid, "-o", "%cpu=")
		if err != nil {
			// ps might fail if the process terminated between pgrep and ps. Log as warning.
			logger.LogMonitor("DEBUG", fmt.Sprintf("CPUCheck: ps command failed for PID %s (process likely terminated): %v (stderr: %s)", pid, err, stderr))
			continue // Skip this PID
		}

		// Parse the float CPU value
		cpuStr := strings.TrimSpace(stdout)
		cpuValFloat, err := strconv.ParseFloat(cpuStr, 64)
		if err != nil {
			logger.LogMonitor("WARNING", fmt.Sprintf("CPUCheck: Failed to parse CPU value '%s' for PID %s: %v", cpuStr, pid, err))
			continue // Skip this PID
		}

		// Add the float value directly
		totalCPU += cpuValFloat
	}
	return totalCPU, nil
}

// checkStateFile checks if the state file exists.
func checkStateFile(path string) bool {
	_, err := os.Stat(path)
	return err == nil // Returns true if file exists, false otherwise
}

// createStateFile creates the empty state file.
func createStateFile(path string, pattern string) {
	f, err := os.Create(path)
	if err != nil {
		logger.LogMonitor("ERROR", fmt.Sprintf("CPUCheck: Failed to create state file '%s' for pattern '%s': %v", path, pattern, err))
		return
	}
	f.Close() // Close the empty file
}

// removeStateFile removes the state file if it exists.
func removeStateFile(path string, pattern string, reason string) {
	if exists := checkStateFile(path); exists {
		err := os.Remove(path)
		if err != nil {
			logger.LogMonitor("ERROR", fmt.Sprintf("CPUCheck: Failed to remove state file '%s' for pattern '%s' (reason: %s): %v", path, pattern, reason, err))
		}
	}
}

// handleHighCPU manages logging and state file updates when CPU is high.
func handleHighCPU(cfg *config.Config, pattern string, cpu float64, severity string, stateFilePath string, stateFileExists bool) {
	if stateFileExists {
		// Already flagged, log sustained high CPU and REMOVE the flag for this cycle
		message := fmt.Sprintf("%s Process '%s' Aggregated CPU Utilization %.1f%% SUSTAINED HIGH", cfg.Hostname, pattern, cpu)
		logger.LogMonitor(severity, message)
		removeStateFile(stateFilePath, pattern, "sustained high CPU")
	} else {
		// First time high, create the state file, DO NOT log yet
		createStateFile(stateFilePath, pattern)
	}
}
