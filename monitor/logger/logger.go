package logger

import (
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"time"
	// Assuming config is in the same module, adjust if needed
)

var (
	hostname      string
	projectCode   string
	monitorLogger *log.Logger
	logFileHandle *os.File
)

func init() {
	var err error
	hostname, err = os.Hostname()
	if err != nil {
		hostname = "unknown"
		// Cannot use logger here yet, print to stderr
		fmt.Fprintf(os.Stderr, "WARN: Could not get hostname in logger init: %v\n", err)
	}
	// We set the output in InitLogger
	log.SetFlags(0)
}

// InitLogger initializes the logger to write to the specified file path.
// It returns the file handle for defer closing and any error.
func InitLogger(logfilePath string, projCode string) (*os.File, error) {
	projectCode = projCode

	// Ensure log directory exists
	logDir := filepath.Dir(logfilePath)
	if err := os.Mkdir<PERSON>ll(logDir, 0755); err != nil {
		return nil, fmt.Errorf("cannot create log directory '%s': %w", logDir, err)
	}

	// Open the log file in append mode, create if it doesn't exist
	f, err := os.OpenFile(logfilePath, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return nil, fmt.Errorf("cannot open log file '%s': %w", logfilePath, err)
	}
	logFileHandle = f

	// Create a logger that writes to the file
	// Remove standard log flags (datetime is added manually in LogMonitor)
	monitorLogger = log.New(f, "", 0)

	// Also set standard log output to stderr for initial/fatal errors
	log.SetOutput(os.Stderr)
	log.SetFlags(0) // Consistent format for startup errors

	return f, nil
}

// LogMonitor logs a message with the standard format: YYMMDD HHMMSS [SEVERITY] HOSTNAME MESSAGE (PROJECT_CODE)
// It handles potential nil logger (before init) by printing to stderr.
func LogMonitor(severity string, message string) {
	// Format timestamp
	now := time.Now().Format("060102 150405") // YYMMDD HHMMSS

	// Truncate message if too long
	const maxMsgLen = 2000
	if len(message) > maxMsgLen {
		message = message[:maxMsgLen]
	}

	// Construct the log entry
	logEntry := fmt.Sprintf("%s [%s] %s (%s)", now, severity, message, projectCode)

	if monitorLogger != nil {
		monitorLogger.Println(logEntry)
	} else {
		// Fallback to stderr if logger not initialized
		fmt.Fprintf(os.Stderr, "%s\n", logEntry)
	}
}

// LogFatalf logs a fatal error to stderr and the initialized logger, then exits.
func LogFatalf(format string, v ...interface{}) {
	msg := fmt.Sprintf(format, v...)
	// Log to initialized logger (which includes stderr)
	log.Printf("FATAL: %s", msg)
	// Ensure it goes to stderr directly in case logger somehow failed
	if log.Writer() != os.Stderr && log.Writer() != io.MultiWriter(os.Stderr) { // Basic check
		fmt.Fprintf(os.Stderr, "FATAL: %s\n", msg)
	}
	os.Exit(1)
}
