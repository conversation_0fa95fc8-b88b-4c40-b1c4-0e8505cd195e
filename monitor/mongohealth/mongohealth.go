package mongohealth

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readpref"

	"monitor/config"
	"monitor/logger"
)

const connectionTimeout = 10 * time.Second

// CheckMongoHealth performs various health checks on the configured MongoDB instance.
func CheckMongoHealth(cfg *config.Config) {
	connectionURL := fmt.Sprintf("mongodb://%s:%d", cfg.MongoHost, cfg.MongoPort)
	clientOptions := options.Client().ApplyURI(connectionURL)

	// Handle Authentication
	if cfg.MongoUser != "" && cfg.MongoPass != "" {
		creds := options.Credential{
			AuthSource: cfg.MongoAuthDB,
			Username:   cfg.MongoUser,
			Password:   cfg.MongoPass,
		}
		clientOptions.SetAuth(creds)
	}

	// Connect to MongoDB
	ctx, cancel := context.WithTimeout(context.Background(), connectionTimeout)
	defer cancel()

	client, err := mongo.Connect(ctx, clientOptions)
	if err != nil {
		logger.LogMonitor("MAJOR", fmt.Sprintf("MongoHealth: Failed to create client to %s:%d: %v", cfg.MongoHost, cfg.MongoPort, err))
		return
	}
	defer func() {
		if err = client.Disconnect(ctx); err != nil {
			logger.LogMonitor("WARNING", fmt.Sprintf("MongoHealth: Error disconnecting from %s:%d: %v", cfg.MongoHost, cfg.MongoPort, err))
		}
	}()

	// Ping the server to verify connection
	pingCtx, pingCancel := context.WithTimeout(context.Background(), 5*time.Second) // Shorter timeout for ping
	defer pingCancel()
	err = client.Ping(pingCtx, readpref.PrimaryPreferred())
	if err != nil {
		logger.LogMonitor("MAJOR", fmt.Sprintf("MongoHealth: Failed to ping %s:%d: %v", cfg.MongoHost, cfg.MongoPort, err))
		return
	}

	// --- Run Checks --- //
	statusCmd := bson.D{bson.E{Key: "serverStatus", Value: 1}}
	var serverStatusResult bson.M
	err = client.Database("admin").RunCommand(ctx, statusCmd).Decode(&serverStatusResult)
	if err != nil {
		logger.LogMonitor("MAJOR", fmt.Sprintf("MongoHealth: Failed to run serverStatus on %s:%d: %v", cfg.MongoHost, cfg.MongoPort, err))
		return // Cannot proceed without serverStatus
	}

	checkServerAsserts(cfg, serverStatusResult)
	checkQueuedOperations(cfg, serverStatusResult)
	checkLocks(cfg, serverStatusResult) // Pass serverStatus for uptime calculation

	// Check replication only if it seems to be part of a replica set
	if _, ok := serverStatusResult["repl"]; ok {
		checkReplicationHealth(ctx, cfg, client)
	} else {
		logger.LogMonitor("INFO", fmt.Sprintf("MongoHealth: Node %s:%d does not appear to be part of a replica set. Skipping replication checks.", cfg.MongoHost, cfg.MongoPort))
	}
}

// checkServerAsserts checks the asserts section of serverStatus.
func checkServerAsserts(cfg *config.Config, status bson.M) {
	asserts, ok := status["asserts"].(bson.M)
	if !ok {
		logger.LogMonitor("WARNING", "MongoHealth: Could not find or parse 'asserts' section in serverStatus.")
		return
	}

	// Check specific assert types for non-zero values
	for _, key := range []string{"regular", "warning", "msg", "user"} {
		val, kOk := asserts[key].(int32) // Asserts are often BSON int32
		if kOk && val > 0 {
			logger.LogMonitor("MAJOR", fmt.Sprintf("MongoHealth: %s MongoDB assert condition detected: %s = %d", cfg.Hostname, key, val))
		}
	}
}

// checkQueuedOperations checks globalLock queues from serverStatus.
func checkQueuedOperations(cfg *config.Config, status bson.M) {
	globalLock, ok := status["globalLock"].(bson.M)
	if !ok {
		logger.LogMonitor("WARNING", "MongoHealth: Could not find or parse 'globalLock' section in serverStatus.")
		return
	}

	currentQueue, ok := globalLock["currentQueue"].(bson.M)
	if !ok {
		logger.LogMonitor("WARNING", "MongoHealth: Could not find or parse 'globalLock.currentQueue' section in serverStatus.")
		return
	}

	totalQueued, tOk := currentQueue["total"].(int32)
	readersQueued, rOk := currentQueue["readers"].(int32)
	writersQueued, wOk := currentQueue["writers"].(int32)

	if tOk && int(totalQueued) >= cfg.QueuedOpsThreshold {
		logger.LogMonitor("MINOR", fmt.Sprintf("MongoHealth: %s MongoDB total queued operations: %d (Readers: %d, Writers: %d) >= threshold %d",
			cfg.Hostname, totalQueued, readersQueued, writersQueued, cfg.QueuedOpsThreshold))
	} else if tOk { // Log if below threshold?
		// logger.LogMonitor("DEBUG", fmt.Sprintf("MongoHealth: %s MongoDB total queued operations: %d (Readers: %d, Writers: %d) OK",
		// 	cfg.Hostname, totalQueued, readersQueued, writersQueued))
	} else if !tOk || !rOk || !wOk {
		logger.LogMonitor("WARNING", "MongoHealth: Failed to parse queued operation counts from serverStatus.")
	}
}

// checkLocks approximates lock percentage using globalLock.totalTime and uptime.
// Note: This is an approximation. Actual lock % from mongostat might differ.
func checkLocks(cfg *config.Config, status bson.M) {
	uptimeMillisFloat, ok := status["uptimeMillis"].(int64) // Often int64
	if !ok {
		uptimeMillisInt, okInt := status["uptimeMillis"].(int32) // Try int32 as fallback
		if okInt {
			uptimeMillisFloat = int64(uptimeMillisInt)
			ok = true
		}
	}
	if !ok || uptimeMillisFloat <= 0 {
		logger.LogMonitor("WARNING", "MongoHealth: Could not find or parse valid 'uptimeMillis' in serverStatus for lock calculation.")
		return
	}

	globalLock, ok := status["globalLock"].(bson.M)
	if !ok {
		// Already logged in checkQueuedOperations if missing
		return
	}

	// Use totalTime since startup (microseconds)
	totalTimeMicros, ok := globalLock["totalTime"].(int64)
	if !ok {
		totalTimeMicrosInt, okInt := globalLock["totalTime"].(int32)
		if okInt {
			totalTimeMicros = int64(totalTimeMicrosInt)
			ok = true
		}
	}
	if !ok {
		logger.LogMonitor("WARNING", "MongoHealth: Could not find or parse 'globalLock.totalTime' in serverStatus for lock calculation.")
		return
	}

	// Convert uptime to microseconds
	uptimeMicros := uptimeMillisFloat * 1000
	if uptimeMicros <= 0 {
		logger.LogMonitor("WARNING", "MongoHealth: Invalid uptimeMicros <= 0 for lock calculation.")
		return
	}

	lockPct := (float64(totalTimeMicros) / float64(uptimeMicros)) * 100.0

	if lockPct >= cfg.LockPctMajorThreshold {
		logger.LogMonitor("MAJOR", fmt.Sprintf("MongoHealth: %s MongoDB global lock percentage approx: %.2f%% >= major threshold %.1f%%", cfg.Hostname, lockPct, cfg.LockPctMajorThreshold))
	} else if lockPct >= cfg.LockPctMinorThreshold {
		logger.LogMonitor("MINOR", fmt.Sprintf("MongoHealth: %s MongoDB global lock percentage approx: %.2f%% >= minor threshold %.1f%%", cfg.Hostname, lockPct, cfg.LockPctMinorThreshold))
	} else {
		// logger.LogMonitor("DEBUG", fmt.Sprintf("MongoHealth: %s MongoDB global lock percentage approx: %.2f%% OK", cfg.Hostname, lockPct))
	}
}

// checkReplicationHealth checks rs.status for state and replication lag.
func checkReplicationHealth(ctx context.Context, cfg *config.Config, client *mongo.Client) {
	replSetCmd := bson.D{bson.E{Key: "replSetGetStatus", Value: 1}}
	var rsStatusResult bson.M
	err := client.Database("admin").RunCommand(ctx, replSetCmd).Decode(&rsStatusResult)
	if err != nil {
		logger.LogMonitor("MAJOR", fmt.Sprintf("MongoHealth: Failed to run replSetGetStatus on %s:%d: %v", cfg.MongoHost, cfg.MongoPort, err))
		return
	}

	// Find 'myState' and member info for the current node
	myStateInt, ok := rsStatusResult["myState"].(int32)
	if !ok {
		logger.LogMonitor("WARNING", "MongoHealth: Could not find or parse 'myState' in replSetGetStatus.")
		return
	}
	myStateStr := mapStateToStr(myStateInt)

	members, ok := rsStatusResult["members"].(primitive.A)
	if !ok {
		logger.LogMonitor("WARNING", "MongoHealth: Could not find or parse 'members' array in replSetGetStatus.")
		return
	}

	var myMemberInfo bson.M
	myName, _ := rsStatusResult["name"].(string)   // Get the replica set name if available
	selfName, _ := rsStatusResult["self"].(string) // Get the host:port string of the current member

	for _, m := range members {
		memberDoc, ok := m.(bson.M)
		if !ok {
			continue
		}
		memberName, _ := memberDoc["name"].(string)
		if memberName == selfName {
			myMemberInfo = memberDoc
			break
		}
	}

	// Check state validity
	if myStateStr == "UNKNOWN" || myStateStr == "REMOVED" || myStateStr == "ROLLBACK" || myStateStr == "DOWN" {
		logger.LogMonitor("MAJOR", fmt.Sprintf("MongoHealth: %s MongoDB node %s (%s) is in an unhealthy state: %s", cfg.Hostname, selfName, myName, myStateStr))
	} else if myStateStr == "STARTUP" || myStateStr == "STARTUP2" || myStateStr == "RECOVERING" {
		logger.LogMonitor("MINOR", fmt.Sprintf("MongoHealth: %s MongoDB node %s (%s) is in state: %s", cfg.Hostname, selfName, myName, myStateStr))
	} // Else: PRIMARY, SECONDARY, ARBITER are generally OK

	// Check replication lag if secondary
	if myStateInt == 2 { // 2 is Secondary state
		var primaryOptime primitive.Timestamp
		foundPrimary := false

		// Find the primary's optime
		for _, m := range members {
			memberDoc, ok := m.(bson.M)
			if !ok {
				continue
			}
			memberState, _ := memberDoc["state"].(int32)
			if memberState == 1 { // 1 is Primary state
				optimeDoc, ok := memberDoc["optime"].(bson.M)
				if ok {
					primaryOptime, _ = optimeDoc["ts"].(primitive.Timestamp)
					foundPrimary = true
					break
				}
			}
		}

		// Get my optime
		myOptime := primitive.Timestamp{}
		if myMemberInfo != nil {
			optimeDoc, ok := myMemberInfo["optime"].(bson.M)
			if ok {
				myOptime, _ = optimeDoc["ts"].(primitive.Timestamp)
			}
		}

		if foundPrimary && myOptime.T > 0 {
			lag := int(primaryOptime.T - myOptime.T)
			if lag >= cfg.ReplLagMajorThreshold {
				logger.LogMonitor("MAJOR", fmt.Sprintf("MongoHealth: %s MongoDB replication lag is %d seconds >= major threshold %d", cfg.Hostname, lag, cfg.ReplLagMajorThreshold))
			} else if lag >= cfg.ReplLagMinorThreshold {
				logger.LogMonitor("MINOR", fmt.Sprintf("MongoHealth: %s MongoDB replication lag is %d seconds >= minor threshold %d", cfg.Hostname, lag, cfg.ReplLagMinorThreshold))
			} else {
				// logger.LogMonitor("DEBUG", fmt.Sprintf("MongoHealth: %s MongoDB replication lag is %d seconds OK", cfg.Hostname, lag))
			}
		} else if !foundPrimary {
			logger.LogMonitor("WARNING", "MongoHealth: Could not find primary member in replica set status to calculate lag.")
		} else if myOptime.T == 0 {
			logger.LogMonitor("WARNING", "MongoHealth: Could not determine secondary's optime to calculate lag.")
		}
	}
}

// mapStateToStr converts replica set member state integer to string.
func mapStateToStr(state int32) string {
	switch state {
	case 0:
		return "STARTUP"
	case 1:
		return "PRIMARY"
	case 2:
		return "SECONDARY"
	case 3:
		return "RECOVERING"
	case 5:
		return "STARTUP2"
	case 6:
		return "UNKNOWN"
	case 7:
		return "ARBITER"
	case 8:
		return "DOWN"
	case 9:
		return "ROLLBACK"
	case 10:
		return "REMOVED"
	default:
		return fmt.Sprintf("UnknownState(%d)", state)
	}
}
 