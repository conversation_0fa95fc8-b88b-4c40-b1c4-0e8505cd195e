package parser

import (
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
	"time"
)

// LogEntry represents the essential information extracted from a log line.
type LogEntry struct {
	Timestamp    time.Time
	SeverityCode string
	RawLine      string
}

// Interface for different log parsers (optional but good practice)
type Parser interface {
	Parse(line string) (*LogEntry, error)
}

// --- JSON Parser ---

type JsonParser struct{}

func NewJsonParser() *JsonParser {
	return &JsonParser{}
}

func (p *JsonParser) Parse(line string) (*LogEntry, error) {
	var logData map[string]interface{}
	err := json.Unmarshal([]byte(line), &logData)
	if err != nil {
		// Return nil, nil for invalid JSON lines, let processor decide to skip
		return nil, fmt.Errorf("invalid json: %w", err)
	}

	entry := &LogEntry{RawLine: line}

	// Extract timestamp
	if tField, ok := logData["t"].(map[string]interface{}); ok {
		if dateStr, ok := tField["$date"].(string); ok {
			parsedTime, err := time.Parse(time.RFC3339Nano, dateStr)
			if err == nil {
				entry.Timestamp = parsedTime.UTC()
			} else {
				return nil, fmt.Errorf("cannot parse timestamp %s: %w", dateStr, err)
			}
		} else {
			return nil, fmt.Errorf("missing $date in timestamp field")
		}
	} else {
		return nil, fmt.Errorf("missing timestamp field 't'")
	}

	// Extract severity
	if sField, ok := logData["s"].(string); ok {
		entry.SeverityCode = sField
	} else {
		return nil, fmt.Errorf("missing severity field 's'")
	}

	return entry, nil
}

// --- Text Parser ---

type TextParser struct {
	SeverityRegex *regexp.Regexp
}

func NewTextParser(severities []string) (*TextParser, error) {
	if len(severities) == 0 {
		return nil, fmt.Errorf("no severities provided for text parser regex")
	}
	// Matches " [F] ", " [E] ", " [W] " etc.
	pattern := fmt.Sprintf(`\s\[([%s])\]\s`, strings.Join(severities, ""))
	re, err := regexp.Compile(pattern)
	if err != nil {
		return nil, fmt.Errorf("failed to compile severity regex: %w", err)
	}
	return &TextParser{SeverityRegex: re}, nil
}

func (p *TextParser) Parse(line string) (*LogEntry, error) {
	entry := &LogEntry{RawLine: line}

	// Extract timestamp
	spaceIndex := strings.Index(line, " ")
	if spaceIndex <= 0 {
		return nil, fmt.Errorf("cannot find space to delimit timestamp")
	}
	timeStr := line[:spaceIndex]
	parsedTime, err := time.Parse(time.RFC3339Nano, timeStr)
	if err != nil {
		// Try without nano
		parsedTime, err = time.Parse("2006-01-02T15:04:05Z07:00", timeStr)
		if err != nil {
			return nil, fmt.Errorf("cannot parse timestamp %s: %w", timeStr, err)
		}
	}
	entry.Timestamp = parsedTime.UTC()

	// Extract severity using Regex
	matches := p.SeverityRegex.FindStringSubmatch(line)
	if len(matches) <= 1 {
		// Could try fallback awk '{print $3}' logic here if needed, but regex is preferred
		return nil, fmt.Errorf("severity pattern not found")
	}
	entry.SeverityCode = matches[1]

	return entry, nil
}
